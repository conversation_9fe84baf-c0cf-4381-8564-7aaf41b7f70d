/**
 * 摄像头输入节点
 * 提供摄像头视频流输入功能
 */
import { VisualScriptNode } from '../VisualScriptNode';
import { NodeCategory } from '../NodeCategory';
import { DataType } from '../DataType';
import { NodeInput, NodeOutput } from '../NodePort';
import { CameraManager, CameraConfig, CameraState } from '../../mocap/camera/CameraManager';
import { Debug } from '../../utils/Debug';

/**
 * 摄像头输入节点配置
 */
export interface CameraInputNodeConfig {
  /** 摄像头设备ID */
  deviceId?: string;
  /** 分辨率 */
  resolution: { width: number; height: number };
  /** 帧率 */
  frameRate: number;
  /** 是否自动启动 */
  autoStart: boolean;
}

/**
 * 摄像头输入节点
 */
export class CameraInputNode extends VisualScriptNode {
  /** 节点类型 */
  public static readonly TYPE = 'CameraInput';
  
  /** 节点名称 */
  public static readonly NAME = '摄像头输入';
  
  /** 节点描述 */
  public static readonly DESCRIPTION = '从摄像头获取视频流数据';
  
  /** 节点分类 */
  public static readonly CATEGORY = NodeCategory.INPUT;

  private cameraManager: CameraManager | null = null;
  private config: CameraInputNodeConfig;
  private isActive = false;
  private lastFrame: ImageData | null = null;
  private frameCount = 0;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: CameraInputNodeConfig = {
    resolution: { width: 640, height: 480 },
    frameRate: 30,
    autoStart: false
  };

  constructor(id: string, config: Partial<CameraInputNodeConfig> = {}) {
    super(id, CameraInputNode.TYPE, CameraInputNode.NAME);
    
    this.config = { ...CameraInputNode.DEFAULT_CONFIG, ...config };
    this.description = CameraInputNode.DESCRIPTION;
    this.category = CameraInputNode.CATEGORY;

    this.setupPorts();
  }

  /**
   * 设置输入输出端口
   */
  private setupPorts(): void {
    // 输入端口
    this.addInput(new NodeInput('start', '启动', DataType.TRIGGER, '启动摄像头'));
    this.addInput(new NodeInput('stop', '停止', DataType.TRIGGER, '停止摄像头'));
    this.addInput(new NodeInput('deviceId', '设备ID', DataType.STRING, '摄像头设备ID'));
    this.addInput(new NodeInput('resolution', '分辨率', DataType.OBJECT, '视频分辨率'));
    this.addInput(new NodeInput('frameRate', '帧率', DataType.NUMBER, '视频帧率'));

    // 输出端口
    this.addOutput(new NodeOutput('frame', '视频帧', DataType.IMAGE, '当前视频帧'));
    this.addOutput(new NodeOutput('imageData', '图像数据', DataType.OBJECT, '图像数据对象'));
    this.addOutput(new NodeOutput('isActive', '是否活跃', DataType.BOOLEAN, '摄像头是否正在运行'));
    this.addOutput(new NodeOutput('frameCount', '帧计数', DataType.NUMBER, '已处理的帧数'));
    this.addOutput(new NodeOutput('fps', 'FPS', DataType.NUMBER, '实际帧率'));
    this.addOutput(new NodeOutput('onStarted', '启动完成', DataType.TRIGGER, '摄像头启动完成时触发'));
    this.addOutput(new NodeOutput('onStopped', '停止完成', DataType.TRIGGER, '摄像头停止完成时触发'));
    this.addOutput(new NodeOutput('onError', '错误', DataType.TRIGGER, '发生错误时触发'));
    this.addOutput(new NodeOutput('onFrame', '新帧', DataType.TRIGGER, '获得新帧时触发'));
  }

  /**
   * 执行节点
   */
  public async execute(): Promise<void> {
    try {
      // 检查输入
      const startTrigger = this.getInputValue('start');
      const stopTrigger = this.getInputValue('stop');
      const deviceId = this.getInputValue('deviceId') as string;
      const resolution = this.getInputValue('resolution') as { width: number; height: number };
      const frameRate = this.getInputValue('frameRate') as number;

      // 更新配置
      if (deviceId !== undefined) {
        this.config.deviceId = deviceId;
      }
      if (resolution) {
        this.config.resolution = resolution;
      }
      if (frameRate !== undefined) {
        this.config.frameRate = frameRate;
      }

      // 处理启动触发
      if (startTrigger && !this.isActive) {
        await this.startCamera();
      }

      // 处理停止触发
      if (stopTrigger && this.isActive) {
        await this.stopCamera();
      }

      // 更新输出
      this.updateOutputs();

    } catch (error) {
      Debug.error('CameraInputNode', '节点执行失败', error);
      this.setOutputValue('onError', true);
      this.triggerOutput('onError');
    }
  }

  /**
   * 启动摄像头
   */
  private async startCamera(): Promise<void> {
    try {
      if (this.cameraManager) {
        await this.cameraManager.stop();
      }

      // 创建摄像头管理器
      const cameraConfig: Partial<CameraConfig> = {
        deviceId: this.config.deviceId,
        resolution: this.config.resolution,
        frameRate: this.config.frameRate,
        audio: false
      };

      this.cameraManager = new CameraManager(cameraConfig);

      // 设置事件监听
      this.setupCameraEvents();

      // 初始化摄像头
      await this.cameraManager.initialize(this.config.deviceId);

      this.isActive = true;
      this.frameCount = 0;

      // 触发启动完成事件
      this.setOutputValue('onStarted', true);
      this.triggerOutput('onStarted');

      Debug.log('CameraInputNode', '摄像头启动成功');

    } catch (error) {
      Debug.error('CameraInputNode', '启动摄像头失败', error);
      this.isActive = false;
      this.setOutputValue('onError', true);
      this.triggerOutput('onError');
      throw error;
    }
  }

  /**
   * 停止摄像头
   */
  private async stopCamera(): Promise<void> {
    try {
      if (this.cameraManager) {
        await this.cameraManager.stop();
        this.cameraManager.destroy();
        this.cameraManager = null;
      }

      this.isActive = false;
      this.lastFrame = null;

      // 触发停止完成事件
      this.setOutputValue('onStopped', true);
      this.triggerOutput('onStopped');

      Debug.log('CameraInputNode', '摄像头已停止');

    } catch (error) {
      Debug.error('CameraInputNode', '停止摄像头失败', error);
      this.setOutputValue('onError', true);
      this.triggerOutput('onError');
      throw error;
    }
  }

  /**
   * 设置摄像头事件监听
   */
  private setupCameraEvents(): void {
    if (!this.cameraManager) return;

    this.cameraManager.on('stateChanged', (newState: CameraState) => {
      if (newState === CameraState.ACTIVE) {
        this.startFrameCapture();
      }
    });

    this.cameraManager.on('error', (error: Error) => {
      Debug.error('CameraInputNode', '摄像头错误', error);
      this.isActive = false;
      this.setOutputValue('onError', true);
      this.triggerOutput('onError');
    });
  }

  /**
   * 开始帧捕获
   */
  private startFrameCapture(): void {
    const captureFrame = () => {
      if (!this.isActive || !this.cameraManager) {
        return;
      }

      try {
        const frame = this.cameraManager.getCurrentFrame();
        if (frame) {
          this.lastFrame = frame;
          this.frameCount++;

          // 触发新帧事件
          this.setOutputValue('onFrame', true);
          this.triggerOutput('onFrame');
        }

        // 继续捕获下一帧
        if (this.isActive) {
          requestAnimationFrame(captureFrame);
        }

      } catch (error) {
        Debug.error('CameraInputNode', '帧捕获失败', error);
      }
    };

    captureFrame();
  }

  /**
   * 更新输出值
   */
  private updateOutputs(): void {
    // 基本状态输出
    this.setOutputValue('isActive', this.isActive);
    this.setOutputValue('frameCount', this.frameCount);

    // 帧数据输出
    if (this.lastFrame) {
      this.setOutputValue('frame', this.lastFrame);
      this.setOutputValue('imageData', this.lastFrame);
    }

    // FPS输出
    if (this.cameraManager) {
      this.setOutputValue('fps', this.cameraManager.getActualFPS());
    }
  }

  /**
   * 初始化节点
   */
  public async initialize(): Promise<void> {
    await super.initialize();

    // 如果配置为自动启动，则启动摄像头
    if (this.config.autoStart) {
      await this.startCamera();
    }
  }

  /**
   * 销毁节点
   */
  public async destroy(): Promise<void> {
    if (this.isActive) {
      await this.stopCamera();
    }

    await super.destroy();
  }

  /**
   * 获取节点配置
   */
  public getConfig(): CameraInputNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新节点配置
   */
  public updateConfig(newConfig: Partial<CameraInputNodeConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 序列化节点数据
   */
  public serialize(): any {
    return {
      ...super.serialize(),
      config: this.config
    };
  }

  /**
   * 反序列化节点数据
   */
  public deserialize(data: any): void {
    super.deserialize(data);
    if (data.config) {
      this.config = { ...this.config, ...data.config };
    }
  }

  /**
   * 获取节点属性配置
   */
  public getPropertyConfig(): any {
    return {
      deviceId: {
        type: 'select',
        label: '摄像头设备',
        description: '选择要使用的摄像头设备',
        options: [], // 这里应该从系统获取可用设备列表
        default: this.config.deviceId
      },
      resolution: {
        type: 'select',
        label: '分辨率',
        description: '视频分辨率设置',
        options: [
          { label: '640x480', value: { width: 640, height: 480 } },
          { label: '1280x720', value: { width: 1280, height: 720 } },
          { label: '1920x1080', value: { width: 1920, height: 1080 } }
        ],
        default: this.config.resolution
      },
      frameRate: {
        type: 'slider',
        label: '帧率',
        description: '视频帧率设置',
        min: 15,
        max: 60,
        step: 15,
        default: this.config.frameRate
      },
      autoStart: {
        type: 'boolean',
        label: '自动启动',
        description: '节点初始化时自动启动摄像头',
        default: this.config.autoStart
      }
    };
  }

  /**
   * 获取当前帧
   */
  public getCurrentFrame(): ImageData | null {
    return this.lastFrame;
  }

  /**
   * 获取摄像头状态
   */
  public getCameraState(): CameraState {
    return this.cameraManager?.getState() || CameraState.IDLE;
  }

  /**
   * 获取帧计数
   */
  public getFrameCount(): number {
    return this.frameCount;
  }

  /**
   * 是否正在运行
   */
  public get active(): boolean {
    return this.isActive;
  }
}
