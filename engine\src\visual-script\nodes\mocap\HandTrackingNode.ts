/**
 * 手部追踪节点
 * 使用MediaPipe进行手部关键点检测和手势识别
 */
import { VisualScriptNode } from '../VisualScriptNode';
import { NodeCategory } from '../NodeCategory';
import { DataType } from '../DataType';
import { NodeInput, NodeOutput } from '../NodePort';
import { MediaPipePoseDetector, HandResults } from '../../mocap/mediapipe/MediaPipePoseDetector';
import { GestureType, GestureResult } from '../../mocap/interaction/ActionMappingSystem';
import { LandmarkData } from '../../mocap/types/LandmarkData';
import { Debug } from '../../utils/Debug';
import { Vector3 } from 'three';

/**
 * 手部追踪节点配置
 */
export interface HandTrackingNodeConfig {
  /** 最大手部数量 */
  maxNumHands: number;
  /** 模型复杂度 (0-1) */
  modelComplexity: number;
  /** 最小检测置信度 */
  minDetectionConfidence: number;
  /** 最小跟踪置信度 */
  minTrackingConfidence: number;
  /** 是否启用手势识别 */
  enableGestureRecognition: boolean;
  /** 手势置信度阈值 */
  gestureConfidenceThreshold: number;
  /** 是否自动初始化 */
  autoInitialize: boolean;
}

/**
 * 手部追踪节点
 */
export class HandTrackingNode extends VisualScriptNode {
  /** 节点类型 */
  public static readonly TYPE = 'HandTracking';

  /** 节点名称 */
  public static readonly NAME = '手部追踪';

  /** 节点描述 */
  public static readonly DESCRIPTION = '使用MediaPipe检测手部关键点和识别手势';

  /** 节点分类 */
  public static readonly CATEGORY = NodeCategory.AI;

  private handDetector: MediaPipePoseDetector | null = null;
  private config: HandTrackingNodeConfig;
  private isInitialized = false;
  private lastResults: HandResults | null = null;
  private lastGestures: { left?: GestureResult; right?: GestureResult } = {};
  private processingCount = 0;
  private successCount = 0;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: HandTrackingNodeConfig = {
    maxNumHands: 2,
    modelComplexity: 1,
    minDetectionConfidence: 0.5,
    minTrackingConfidence: 0.5,
    enableGestureRecognition: true,
    gestureConfidenceThreshold: 0.6,
    autoInitialize: true
  };

  constructor(id: string, config: Partial<HandTrackingNodeConfig> = {}) {
    super(id, HandTrackingNode.TYPE, HandTrackingNode.NAME);

    this.config = { ...HandTrackingNode.DEFAULT_CONFIG, ...config };
    this.description = HandTrackingNode.DESCRIPTION;
    this.category = HandTrackingNode.CATEGORY;

    this.setupPorts();
  }

  /**
   * 设置输入输出端口
   */
  private setupPorts(): void {
    // 输入端口
    this.addInput(new NodeInput('imageData', '图像数据', DataType.IMAGE, '要检测的图像数据'));
    this.addInput(new NodeInput('detect', '检测', DataType.TRIGGER, '触发手部检测'));
    this.addInput(new NodeInput('initialize', '初始化', DataType.TRIGGER, '初始化检测器'));
    this.addInput(new NodeInput('maxNumHands', '最大手数', DataType.NUMBER, '最大检测手部数量'));
    this.addInput(new NodeInput('minDetectionConfidence', '检测置信度', DataType.NUMBER, '最小检测置信度'));
    this.addInput(new NodeInput('enableGestureRecognition', '启用手势识别', DataType.BOOLEAN, '是否启用手势识别'));

    // 基本输出端口
    this.addOutput(new NodeOutput('leftHand', '左手关键点', DataType.ARRAY, '左手关键点数组'));
    this.addOutput(new NodeOutput('rightHand', '右手关键点', DataType.ARRAY, '右手关键点数组'));
    this.addOutput(new NodeOutput('handedness', '手部分类', DataType.ARRAY, '手部分类信息'));
    this.addOutput(new NodeOutput('confidence', '置信度', DataType.NUMBER, '检测置信度'));
    this.addOutput(new NodeOutput('handsDetected', '检测到手部', DataType.BOOLEAN, '是否检测到手部'));
    this.addOutput(new NodeOutput('handCount', '手部数量', DataType.NUMBER, '检测到的手部数量'));
    this.addOutput(new NodeOutput('processingTime', '处理时间', DataType.NUMBER, '处理时间(毫秒)'));
    this.addOutput(new NodeOutput('successRate', '成功率', DataType.NUMBER, '检测成功率'));

    // 事件输出端口
    this.addOutput(new NodeOutput('onDetected', '检测完成', DataType.TRIGGER, '检测完成时触发'));
    this.addOutput(new NodeOutput('onInitialized', '初始化完成', DataType.TRIGGER, '初始化完成时触发'));
    this.addOutput(new NodeOutput('onError', '错误', DataType.TRIGGER, '发生错误时触发'));

    // 手势识别输出端口
    this.addOutput(new NodeOutput('leftGesture', '左手手势', DataType.OBJECT, '左手手势识别结果'));
    this.addOutput(new NodeOutput('rightGesture', '右手手势', DataType.OBJECT, '右手手势识别结果'));
    this.addOutput(new NodeOutput('onGestureChanged', '手势变化', DataType.TRIGGER, '手势发生变化时触发'));

    // 左手关键点输出
    this.addOutput(new NodeOutput('leftWrist', '左手腕', DataType.OBJECT, '左手腕关键点'));
    this.addOutput(new NodeOutput('leftThumb', '左拇指', DataType.ARRAY, '左拇指关键点'));
    this.addOutput(new NodeOutput('leftIndex', '左食指', DataType.ARRAY, '左食指关键点'));
    this.addOutput(new NodeOutput('leftMiddle', '左中指', DataType.ARRAY, '左中指关键点'));
    this.addOutput(new NodeOutput('leftRing', '左无名指', DataType.ARRAY, '左无名指关键点'));
    this.addOutput(new NodeOutput('leftPinky', '左小指', DataType.ARRAY, '左小指关键点'));

    // 右手关键点输出
    this.addOutput(new NodeOutput('rightWrist', '右手腕', DataType.OBJECT, '右手腕关键点'));
    this.addOutput(new NodeOutput('rightThumb', '右拇指', DataType.ARRAY, '右拇指关键点'));
    this.addOutput(new NodeOutput('rightIndex', '右食指', DataType.ARRAY, '右食指关键点'));
    this.addOutput(new NodeOutput('rightMiddle', '右中指', DataType.ARRAY, '右中指关键点'));
    this.addOutput(new NodeOutput('rightRing', '右无名指', DataType.ARRAY, '右无名指关键点'));
    this.addOutput(new NodeOutput('rightPinky', '右小指', DataType.ARRAY, '右小指关键点'));

    // 手部位置和方向输出
    this.addOutput(new NodeOutput('leftHandPosition', '左手位置', DataType.VECTOR3, '左手中心位置'));
    this.addOutput(new NodeOutput('rightHandPosition', '右手位置', DataType.VECTOR3, '右手中心位置'));
    this.addOutput(new NodeOutput('leftHandRotation', '左手旋转', DataType.VECTOR3, '左手旋转角度'));
    this.addOutput(new NodeOutput('rightHandRotation', '右手旋转', DataType.VECTOR3, '右手旋转角度'));
  }

  /**
   * 执行节点
   */
  public async execute(): Promise<void> {
    try {
      // 检查输入
      const imageData = this.getInputValue('imageData') as ImageData;
      const detectTrigger = this.getInputValue('detect');
      const initializeTrigger = this.getInputValue('initialize');
      const maxNumHands = this.getInputValue('maxNumHands') as number;
      const minDetectionConfidence = this.getInputValue('minDetectionConfidence') as number;
      const enableGestureRecognition = this.getInputValue('enableGestureRecognition') as boolean;

      // 更新配置
      if (maxNumHands !== undefined) {
        this.config.maxNumHands = Math.max(1, Math.min(4, Math.floor(maxNumHands)));
      }
      if (minDetectionConfidence !== undefined) {
        this.config.minDetectionConfidence = Math.max(0, Math.min(1, minDetectionConfidence));
      }
      if (enableGestureRecognition !== undefined) {
        this.config.enableGestureRecognition = enableGestureRecognition;
      }

      // 处理初始化触发
      if (initializeTrigger || (!this.isInitialized && this.config.autoInitialize)) {
        await this.initializeDetector();
      }

      // 处理检测触发
      if (detectTrigger && imageData && this.isInitialized) {
        await this.detectHands(imageData);
      }

      // 更新输出
      this.updateOutputs();

    } catch (error) {
      Debug.error('HandTrackingNode', '节点执行失败', error);
      this.setOutputValue('onError', true);
      this.triggerOutput('onError');
    }
  }

  /**
   * 初始化检测器
   */
  private async initializeDetector(): Promise<void> {
    try {
      if (this.handDetector) {
        this.handDetector.destroy();
      }

      // 创建MediaPipe配置
      const mediaPipeConfig = {
        modelComplexity: this.config.modelComplexity,
        minDetectionConfidence: this.config.minDetectionConfidence,
        minTrackingConfidence: this.config.minTrackingConfidence
      };

      this.handDetector = new MediaPipePoseDetector(mediaPipeConfig);

      // 设置事件监听
      this.setupDetectorEvents();

      // 初始化
      await this.handDetector.initialize();

      this.isInitialized = true;
      this.processingCount = 0;
      this.successCount = 0;

      // 触发初始化完成事件
      this.setOutputValue('onInitialized', true);
      this.triggerOutput('onInitialized');

      Debug.log('HandTrackingNode', 'MediaPipe手部检测器初始化成功');

    } catch (error) {
      Debug.error('HandTrackingNode', '初始化检测器失败', error);
      this.isInitialized = false;
      this.setOutputValue('onError', true);
      this.triggerOutput('onError');
      throw error;
    }
  }

  /**
   * 设置检测器事件监听
   */
  private setupDetectorEvents(): void {
    if (!this.handDetector) return;

    this.handDetector.on('handsDetected', (results: HandResults, processingTime: number) => {
      this.lastResults = results;
      this.successCount++;

      // 处理手势识别
      if (this.config.enableGestureRecognition) {
        this.processGestureRecognition(results);
      }

      this.setOutputValue('processingTime', processingTime);
      this.setOutputValue('onDetected', true);
      this.triggerOutput('onDetected');
    });

    this.handDetector.on('error', (error: Error) => {
      Debug.error('HandTrackingNode', 'MediaPipe检测错误', error);
      this.setOutputValue('onError', true);
      this.triggerOutput('onError');
    });
  }

  /**
   * 检测手部
   */
  private async detectHands(imageData: ImageData): Promise<void> {
    if (!this.handDetector || !this.isInitialized) {
      throw new Error('检测器未初始化');
    }

    try {
      this.processingCount++;

      const startTime = performance.now();
      const results = await this.handDetector.detectHands(imageData);
      const processingTime = performance.now() - startTime;

      this.lastResults = results;
      this.successCount++;

      // 处理手势识别
      if (this.config.enableGestureRecognition) {
        this.processGestureRecognition(results);
      }

      // 更新输出
      this.setOutputValue('processingTime', processingTime);
      this.setOutputValue('onDetected', true);
      this.triggerOutput('onDetected');

    } catch (error) {
      Debug.error('HandTrackingNode', '手部检测失败', error);
      this.setOutputValue('onError', true);
      this.triggerOutput('onError');
      throw error;
    }
  }

  /**
   * 处理手势识别
   */
  private processGestureRecognition(results: HandResults): void {
    const newGestures: { left?: GestureResult; right?: GestureResult } = {};
    let gestureChanged = false;

    // 识别左手手势
    if (results.leftHand) {
      const leftGesture = this.recognizeGesture(results.leftHand, 'left');
      if (leftGesture && leftGesture.confidence > this.config.gestureConfidenceThreshold) {
        newGestures.left = leftGesture;

        if (!this.lastGestures.left || this.lastGestures.left.type !== leftGesture.type) {
          gestureChanged = true;
        }
      }
    }

    // 识别右手手势
    if (results.rightHand) {
      const rightGesture = this.recognizeGesture(results.rightHand, 'right');
      if (rightGesture && rightGesture.confidence > this.config.gestureConfidenceThreshold) {
        newGestures.right = rightGesture;

        if (!this.lastGestures.right || this.lastGestures.right.type !== rightGesture.type) {
          gestureChanged = true;
        }
      }
    }

    // 更新手势状态
    this.lastGestures = newGestures;

    // 如果手势发生变化，触发事件
    if (gestureChanged) {
      this.setOutputValue('onGestureChanged', true);
      this.triggerOutput('onGestureChanged');
    }
  }

  /**
   * 识别手势
   */
  private recognizeGesture(handLandmarks: LandmarkData[], handType: 'left' | 'right'): GestureResult | null {
    if (handLandmarks.length < 21) {
      return null;
    }

    try {
      // 计算手指弯曲度
      const fingerCurvatures = this.calculateFingerCurvatures(handLandmarks);

      // 计算手部位置
      const handPosition = this.calculateHandPosition(handLandmarks);

      // 抓取手势检测
      if (this.isGrabGesture(fingerCurvatures)) {
        return {
          type: GestureType.GRAB,
          confidence: this.calculateGrabConfidence(fingerCurvatures),
          hand: handType,
          position: handPosition,
          timestamp: Date.now()
        };
      }

      // 张开手势检测
      if (this.isOpenHandGesture(fingerCurvatures)) {
        return {
          type: GestureType.OPEN_HAND,
          confidence: this.calculateOpenHandConfidence(fingerCurvatures),
          hand: handType,
          position: handPosition,
          timestamp: Date.now()
        };
      }

      // 指向手势检测
      if (this.isPointingGesture(fingerCurvatures)) {
        return {
          type: GestureType.POINTING,
          confidence: this.calculatePointingConfidence(fingerCurvatures),
          hand: handType,
          position: handPosition,
          timestamp: Date.now()
        };
      }

      // 竖拇指手势检测
      if (this.isThumbsUpGesture(fingerCurvatures)) {
        return {
          type: GestureType.THUMBS_UP,
          confidence: this.calculateThumbsUpConfidence(fingerCurvatures),
          hand: handType,
          position: handPosition,
          timestamp: Date.now()
        };
      }

    } catch (error) {
      Debug.error('HandTrackingNode', '手势识别失败', error);
    }

    return null;
  }

  /**
   * 计算手指弯曲度
   */
  private calculateFingerCurvatures(handLandmarks: LandmarkData[]): any {
    // MediaPipe手部关键点索引
    const fingerTips = [4, 8, 12, 16, 20]; // 拇指、食指、中指、无名指、小指指尖
    const fingerPips = [3, 6, 10, 14, 18]; // 对应的PIP关节
    const fingerMcps = [2, 5, 9, 13, 17]; // 对应的MCP关节

    const curvatures = {
      thumb: 0,
      index: 0,
      middle: 0,
      ring: 0,
      pinky: 0
    };

    const fingerNames = ['thumb', 'index', 'middle', 'ring', 'pinky'] as const;

    for (let i = 0; i < 5; i++) {
      const tip = handLandmarks[fingerTips[i]];
      const pip = handLandmarks[fingerPips[i]];
      const mcp = handLandmarks[fingerMcps[i]];

      if (tip && pip && mcp) {
        // 计算弯曲度：指尖到MCP的距离 vs PIP到MCP的距离
        const tipToMcp = Math.sqrt(
          Math.pow(tip.x - mcp.x, 2) +
          Math.pow(tip.y - mcp.y, 2)
        );
        const pipToMcp = Math.sqrt(
          Math.pow(pip.x - mcp.x, 2) +
          Math.pow(pip.y - mcp.y, 2)
        );

        // 弯曲度 = 1 - (实际距离 / 最大可能距离)
        curvatures[fingerNames[i]] = Math.max(0, 1 - (tipToMcp / (pipToMcp * 2)));
      }
    }

    return curvatures;
  }

  /**
   * 计算手部位置
   */
  private calculateHandPosition(handLandmarks: LandmarkData[]): Vector3 {
    // 使用手腕位置作为手部中心
    const wrist = handLandmarks[0]; // MediaPipe手部关键点0是手腕

    if (wrist) {
      return new Vector3(
        (wrist.x - 0.5) * 2, // 转换到[-1, 1]范围
        -(wrist.y - 0.5) * 2, // Y轴翻转
        wrist.z || 0
      );
    }

    return new Vector3();
  }

  /**
   * 检测抓取手势
   */
  private isGrabGesture(curvatures: any): boolean {
    const threshold = 0.7;
    return curvatures.index > threshold &&
           curvatures.middle > threshold &&
           curvatures.ring > threshold &&
           curvatures.pinky > threshold;
  }

  /**
   * 检测张开手势
   */
  private isOpenHandGesture(curvatures: any): boolean {
    const threshold = 0.3;
    return curvatures.thumb < threshold &&
           curvatures.index < threshold &&
           curvatures.middle < threshold &&
           curvatures.ring < threshold &&
           curvatures.pinky < threshold;
  }

  /**
   * 检测指向手势
   */
  private isPointingGesture(curvatures: any): boolean {
    const straightThreshold = 0.3;
    const bentThreshold = 0.7;
    return curvatures.index < straightThreshold &&
           curvatures.middle > bentThreshold &&
           curvatures.ring > bentThreshold &&
           curvatures.pinky > bentThreshold;
  }

  /**
   * 检测竖拇指手势
   */
  private isThumbsUpGesture(curvatures: any): boolean {
    const straightThreshold = 0.3;
    const bentThreshold = 0.7;
    return curvatures.thumb < straightThreshold &&
           curvatures.index > bentThreshold &&
           curvatures.middle > bentThreshold &&
           curvatures.ring > bentThreshold &&
           curvatures.pinky > bentThreshold;
  }

  /**
   * 计算各种手势的置信度
   */
  private calculateGrabConfidence(curvatures: any): number {
    const avgCurvature = (curvatures.index + curvatures.middle + curvatures.ring + curvatures.pinky) / 4;
    return Math.min(1, avgCurvature);
  }

  private calculateOpenHandConfidence(curvatures: any): number {
    const avgStraightness = 1 - (curvatures.thumb + curvatures.index + curvatures.middle + curvatures.ring + curvatures.pinky) / 5;
    return Math.max(0, avgStraightness);
  }

  private calculatePointingConfidence(curvatures: any): number {
    const indexStraightness = 1 - curvatures.index;
    const othersCurvature = (curvatures.middle + curvatures.ring + curvatures.pinky) / 3;
    return (indexStraightness + othersCurvature) / 2;
  }

  private calculateThumbsUpConfidence(curvatures: any): number {
    const thumbStraightness = 1 - curvatures.thumb;
    const othersCurvature = (curvatures.index + curvatures.middle + curvatures.ring + curvatures.pinky) / 4;
    return (thumbStraightness + othersCurvature) / 2;
  }

  /**
   * 更新输出值
   */
  private updateOutputs(): void {
    if (!this.lastResults) {
      this.setOutputValue('handsDetected', false);
      this.setOutputValue('handCount', 0);
      return;
    }

    const results = this.lastResults;

    // 基本输出
    this.setOutputValue('leftHand', results.leftHand || []);
    this.setOutputValue('rightHand', results.rightHand || []);
    this.setOutputValue('handedness', results.handedness || []);
    this.setOutputValue('confidence', results.confidence);

    const handCount = (results.leftHand ? 1 : 0) + (results.rightHand ? 1 : 0);
    this.setOutputValue('handsDetected', handCount > 0);
    this.setOutputValue('handCount', handCount);

    // 统计信息
    const successRate = this.processingCount > 0 ? this.successCount / this.processingCount : 0;
    this.setOutputValue('successRate', successRate);

    // 手势识别输出
    this.setOutputValue('leftGesture', this.lastGestures.left || null);
    this.setOutputValue('rightGesture', this.lastGestures.right || null);

    // 左手关键点输出
    if (results.leftHand && results.leftHand.length >= 21) {
      const leftHand = results.leftHand;
      this.setOutputValue('leftWrist', leftHand[0]);
      this.setOutputValue('leftThumb', leftHand.slice(1, 5));
      this.setOutputValue('leftIndex', leftHand.slice(5, 9));
      this.setOutputValue('leftMiddle', leftHand.slice(9, 13));
      this.setOutputValue('leftRing', leftHand.slice(13, 17));
      this.setOutputValue('leftPinky', leftHand.slice(17, 21));

      // 左手位置和旋转
      this.setOutputValue('leftHandPosition', this.calculateHandPosition(leftHand));
      this.setOutputValue('leftHandRotation', this.calculateHandRotation(leftHand));
    }

    // 右手关键点输出
    if (results.rightHand && results.rightHand.length >= 21) {
      const rightHand = results.rightHand;
      this.setOutputValue('rightWrist', rightHand[0]);
      this.setOutputValue('rightThumb', rightHand.slice(1, 5));
      this.setOutputValue('rightIndex', rightHand.slice(5, 9));
      this.setOutputValue('rightMiddle', rightHand.slice(9, 13));
      this.setOutputValue('rightRing', rightHand.slice(13, 17));
      this.setOutputValue('rightPinky', rightHand.slice(17, 21));

      // 右手位置和旋转
      this.setOutputValue('rightHandPosition', this.calculateHandPosition(rightHand));
      this.setOutputValue('rightHandRotation', this.calculateHandRotation(rightHand));
    }
  }

  /**
   * 计算手部旋转
   */
  private calculateHandRotation(handLandmarks: LandmarkData[]): Vector3 {
    if (handLandmarks.length < 21) {
      return new Vector3();
    }

    // 使用手腕、中指MCP和食指MCP计算手部方向
    const wrist = handLandmarks[0];
    const middleMcp = handLandmarks[9];
    const indexMcp = handLandmarks[5];

    if (wrist && middleMcp && indexMcp) {
      // 计算手部向前方向（手腕到中指MCP）
      const forward = new Vector3(
        middleMcp.x - wrist.x,
        middleMcp.y - wrist.y,
        (middleMcp.z || 0) - (wrist.z || 0)
      ).normalize();

      // 计算手部右方向（手腕到食指MCP）
      const right = new Vector3(
        indexMcp.x - wrist.x,
        indexMcp.y - wrist.y,
        (indexMcp.z || 0) - (wrist.z || 0)
      ).normalize();

      // 计算手部上方向
      const up = new Vector3().crossVectors(forward, right).normalize();

      // 转换为欧拉角
      const pitch = Math.asin(-forward.y);
      const yaw = Math.atan2(forward.x, forward.z);
      const roll = Math.atan2(right.y, up.y);

      return new Vector3(pitch, yaw, roll);
    }

    return new Vector3();
  }

  /**
   * 初始化节点
   */
  public async initialize(): Promise<void> {
    await super.initialize();

    // 如果配置为自动初始化，则初始化检测器
    if (this.config.autoInitialize) {
      await this.initializeDetector();
    }
  }

  /**
   * 销毁节点
   */
  public async destroy(): Promise<void> {
    if (this.handDetector) {
      this.handDetector.destroy();
      this.handDetector = null;
    }

    this.isInitialized = false;
    this.lastResults = null;
    this.lastGestures = {};

    await super.destroy();
  }

  /**
   * 获取节点配置
   */
  public getConfig(): HandTrackingNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新节点配置
   */
  public updateConfig(newConfig: Partial<HandTrackingNodeConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 序列化节点数据
   */
  public serialize(): any {
    return {
      ...super.serialize(),
      config: this.config
    };
  }

  /**
   * 反序列化节点数据
   */
  public deserialize(data: any): void {
    super.deserialize(data);
    if (data.config) {
      this.config = { ...this.config, ...data.config };
    }
  }

  /**
   * 获取节点属性配置
   */
  public getPropertyConfig(): any {
    return {
      maxNumHands: {
        type: 'slider',
        label: '最大手数',
        description: '最大检测手部数量',
        min: 1,
        max: 4,
        step: 1,
        default: this.config.maxNumHands
      },
      modelComplexity: {
        type: 'slider',
        label: '模型复杂度',
        description: '模型复杂度，越高精度越好但速度越慢',
        min: 0,
        max: 1,
        step: 1,
        default: this.config.modelComplexity
      },
      minDetectionConfidence: {
        type: 'slider',
        label: '检测置信度',
        description: '最小检测置信度阈值',
        min: 0,
        max: 1,
        step: 0.1,
        default: this.config.minDetectionConfidence
      },
      minTrackingConfidence: {
        type: 'slider',
        label: '跟踪置信度',
        description: '最小跟踪置信度阈值',
        min: 0,
        max: 1,
        step: 0.1,
        default: this.config.minTrackingConfidence
      },
      enableGestureRecognition: {
        type: 'boolean',
        label: '启用手势识别',
        description: '启用手势识别功能',
        default: this.config.enableGestureRecognition
      },
      gestureConfidenceThreshold: {
        type: 'slider',
        label: '手势置信度阈值',
        description: '手势识别置信度阈值',
        min: 0,
        max: 1,
        step: 0.1,
        default: this.config.gestureConfidenceThreshold
      },
      autoInitialize: {
        type: 'boolean',
        label: '自动初始化',
        description: '节点创建时自动初始化检测器',
        default: this.config.autoInitialize
      }
    };
  }

  /**
   * 获取最后检测结果
   */
  public getLastResults(): HandResults | null {
    return this.lastResults;
  }

  /**
   * 获取最后手势结果
   */
  public getLastGestures(): { left?: GestureResult; right?: GestureResult } {
    return { ...this.lastGestures };
  }

  /**
   * 获取检测成功率
   */
  public getSuccessRate(): number {
    return this.processingCount > 0 ? this.successCount / this.processingCount : 0;
  }

  /**
   * 是否已初始化
   */
  public get initialized(): boolean {
    return this.isInitialized;
  }
}