/**
 * 姿态检测节点
 * 使用MediaPipe进行人体姿态检测
 */
import { VisualScriptNode } from '../VisualScriptNode';
import { NodeCategory } from '../NodeCategory';
import { DataType } from '../DataType';
import { NodeInput, NodeOutput } from '../NodePort';
import { MediaPipePoseDetector, MediaPipeConfig, PoseResults } from '../../mocap/mediapipe/MediaPipePoseDetector';
import { LandmarkData, WorldLandmarkData } from '../../mocap/types/LandmarkData';
import { Debug } from '../../utils/Debug';

/**
 * 姿态检测节点配置
 */
export interface PoseDetectionNodeConfig {
  /** 模型复杂度 (0-2) */
  modelComplexity: number;
  /** 是否启用平滑 */
  smoothLandmarks: boolean;
  /** 是否启用分割 */
  enableSegmentation: boolean;
  /** 最小检测置信度 */
  minDetectionConfidence: number;
  /** 最小跟踪置信度 */
  minTrackingConfidence: number;
  /** 是否启用世界坐标 */
  enableWorldLandmarks: boolean;
  /** 是否自动初始化 */
  autoInitialize: boolean;
}

/**
 * 姿态检测节点
 */
export class PoseDetectionNode extends VisualScriptNode {
  /** 节点类型 */
  public static readonly TYPE = 'PoseDetection';
  
  /** 节点名称 */
  public static readonly NAME = '姿态检测';
  
  /** 节点描述 */
  public static readonly DESCRIPTION = '使用MediaPipe检测人体姿态关键点';
  
  /** 节点分类 */
  public static readonly CATEGORY = NodeCategory.AI;

  private poseDetector: MediaPipePoseDetector | null = null;
  private config: PoseDetectionNodeConfig;
  private isInitialized = false;
  private lastResults: PoseResults | null = null;
  private processingCount = 0;
  private successCount = 0;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: PoseDetectionNodeConfig = {
    modelComplexity: 1,
    smoothLandmarks: true,
    enableSegmentation: false,
    minDetectionConfidence: 0.5,
    minTrackingConfidence: 0.5,
    enableWorldLandmarks: true,
    autoInitialize: true
  };

  constructor(id: string, config: Partial<PoseDetectionNodeConfig> = {}) {
    super(id, PoseDetectionNode.TYPE, PoseDetectionNode.NAME);
    
    this.config = { ...PoseDetectionNode.DEFAULT_CONFIG, ...config };
    this.description = PoseDetectionNode.DESCRIPTION;
    this.category = PoseDetectionNode.CATEGORY;

    this.setupPorts();
  }

  /**
   * 设置输入输出端口
   */
  private setupPorts(): void {
    // 输入端口
    this.addInput(new NodeInput('imageData', '图像数据', DataType.IMAGE, '要检测的图像数据'));
    this.addInput(new NodeInput('detect', '检测', DataType.TRIGGER, '触发姿态检测'));
    this.addInput(new NodeInput('initialize', '初始化', DataType.TRIGGER, '初始化检测器'));
    this.addInput(new NodeInput('modelComplexity', '模型复杂度', DataType.NUMBER, '模型复杂度 (0-2)'));
    this.addInput(new NodeInput('minDetectionConfidence', '检测置信度', DataType.NUMBER, '最小检测置信度'));
    this.addInput(new NodeInput('minTrackingConfidence', '跟踪置信度', DataType.NUMBER, '最小跟踪置信度'));

    // 输出端口
    this.addOutput(new NodeOutput('landmarks', '2D关键点', DataType.ARRAY, '2D姿态关键点数组'));
    this.addOutput(new NodeOutput('worldLandmarks', '3D关键点', DataType.ARRAY, '3D世界坐标关键点数组'));
    this.addOutput(new NodeOutput('segmentationMask', '分割掩码', DataType.IMAGE, '人体分割掩码'));
    this.addOutput(new NodeOutput('confidence', '置信度', DataType.NUMBER, '检测置信度'));
    this.addOutput(new NodeOutput('isDetected', '检测成功', DataType.BOOLEAN, '是否检测到人体'));
    this.addOutput(new NodeOutput('processingTime', '处理时间', DataType.NUMBER, '处理时间(毫秒)'));
    this.addOutput(new NodeOutput('successRate', '成功率', DataType.NUMBER, '检测成功率'));
    this.addOutput(new NodeOutput('onDetected', '检测完成', DataType.TRIGGER, '检测完成时触发'));
    this.addOutput(new NodeOutput('onInitialized', '初始化完成', DataType.TRIGGER, '初始化完成时触发'));
    this.addOutput(new NodeOutput('onError', '错误', DataType.TRIGGER, '发生错误时触发'));

    // 特定关键点输出
    this.addOutput(new NodeOutput('nose', '鼻子', DataType.OBJECT, '鼻子关键点'));
    this.addOutput(new NodeOutput('leftEye', '左眼', DataType.OBJECT, '左眼关键点'));
    this.addOutput(new NodeOutput('rightEye', '右眼', DataType.OBJECT, '右眼关键点'));
    this.addOutput(new NodeOutput('leftShoulder', '左肩', DataType.OBJECT, '左肩关键点'));
    this.addOutput(new NodeOutput('rightShoulder', '右肩', DataType.OBJECT, '右肩关键点'));
    this.addOutput(new NodeOutput('leftElbow', '左肘', DataType.OBJECT, '左肘关键点'));
    this.addOutput(new NodeOutput('rightElbow', '右肘', DataType.OBJECT, '右肘关键点'));
    this.addOutput(new NodeOutput('leftWrist', '左腕', DataType.OBJECT, '左腕关键点'));
    this.addOutput(new NodeOutput('rightWrist', '右腕', DataType.OBJECT, '右腕关键点'));
    this.addOutput(new NodeOutput('leftHip', '左髋', DataType.OBJECT, '左髋关键点'));
    this.addOutput(new NodeOutput('rightHip', '右髋', DataType.OBJECT, '右髋关键点'));
    this.addOutput(new NodeOutput('leftKnee', '左膝', DataType.OBJECT, '左膝关键点'));
    this.addOutput(new NodeOutput('rightKnee', '右膝', DataType.OBJECT, '右膝关键点'));
    this.addOutput(new NodeOutput('leftAnkle', '左踝', DataType.OBJECT, '左踝关键点'));
    this.addOutput(new NodeOutput('rightAnkle', '右踝', DataType.OBJECT, '右踝关键点'));
  }

  /**
   * 执行节点
   */
  public async execute(): Promise<void> {
    try {
      // 检查输入
      const imageData = this.getInputValue('imageData') as ImageData;
      const detectTrigger = this.getInputValue('detect');
      const initializeTrigger = this.getInputValue('initialize');
      const modelComplexity = this.getInputValue('modelComplexity') as number;
      const minDetectionConfidence = this.getInputValue('minDetectionConfidence') as number;
      const minTrackingConfidence = this.getInputValue('minTrackingConfidence') as number;

      // 更新配置
      if (modelComplexity !== undefined) {
        this.config.modelComplexity = Math.max(0, Math.min(2, Math.floor(modelComplexity)));
      }
      if (minDetectionConfidence !== undefined) {
        this.config.minDetectionConfidence = Math.max(0, Math.min(1, minDetectionConfidence));
      }
      if (minTrackingConfidence !== undefined) {
        this.config.minTrackingConfidence = Math.max(0, Math.min(1, minTrackingConfidence));
      }

      // 处理初始化触发
      if (initializeTrigger || (!this.isInitialized && this.config.autoInitialize)) {
        await this.initializeDetector();
      }

      // 处理检测触发
      if (detectTrigger && imageData && this.isInitialized) {
        await this.detectPose(imageData);
      }

      // 更新输出
      this.updateOutputs();

    } catch (error) {
      Debug.error('PoseDetectionNode', '节点执行失败', error);
      this.setOutputValue('onError', true);
      this.triggerOutput('onError');
    }
  }

  /**
   * 初始化检测器
   */
  private async initializeDetector(): Promise<void> {
    try {
      if (this.poseDetector) {
        this.poseDetector.destroy();
      }

      // 创建MediaPipe配置
      const mediaPipeConfig: Partial<MediaPipeConfig> = {
        modelComplexity: this.config.modelComplexity,
        smoothLandmarks: this.config.smoothLandmarks,
        enableSegmentation: this.config.enableSegmentation,
        minDetectionConfidence: this.config.minDetectionConfidence,
        minTrackingConfidence: this.config.minTrackingConfidence,
        enableWorldLandmarks: this.config.enableWorldLandmarks
      };

      this.poseDetector = new MediaPipePoseDetector(mediaPipeConfig);

      // 设置事件监听
      this.setupDetectorEvents();

      // 初始化
      await this.poseDetector.initialize();

      this.isInitialized = true;
      this.processingCount = 0;
      this.successCount = 0;

      // 触发初始化完成事件
      this.setOutputValue('onInitialized', true);
      this.triggerOutput('onInitialized');

      Debug.log('PoseDetectionNode', 'MediaPipe姿态检测器初始化成功');

    } catch (error) {
      Debug.error('PoseDetectionNode', '初始化检测器失败', error);
      this.isInitialized = false;
      this.setOutputValue('onError', true);
      this.triggerOutput('onError');
      throw error;
    }
  }

  /**
   * 设置检测器事件监听
   */
  private setupDetectorEvents(): void {
    if (!this.poseDetector) return;

    this.poseDetector.on('poseDetected', (results: PoseResults, processingTime: number) => {
      this.lastResults = results;
      this.successCount++;
      
      this.setOutputValue('processingTime', processingTime);
      this.setOutputValue('onDetected', true);
      this.triggerOutput('onDetected');
    });

    this.poseDetector.on('error', (error: Error) => {
      Debug.error('PoseDetectionNode', 'MediaPipe检测错误', error);
      this.setOutputValue('onError', true);
      this.triggerOutput('onError');
    });
  }

  /**
   * 检测姿态
   */
  private async detectPose(imageData: ImageData): Promise<void> {
    if (!this.poseDetector || !this.isInitialized) {
      throw new Error('检测器未初始化');
    }

    try {
      this.processingCount++;
      
      const startTime = performance.now();
      const results = await this.poseDetector.detectPose(imageData);
      const processingTime = performance.now() - startTime;

      this.lastResults = results;
      this.successCount++;

      // 更新输出
      this.setOutputValue('processingTime', processingTime);
      this.setOutputValue('onDetected', true);
      this.triggerOutput('onDetected');

    } catch (error) {
      Debug.error('PoseDetectionNode', '姿态检测失败', error);
      this.setOutputValue('onError', true);
      this.triggerOutput('onError');
      throw error;
    }
  }

  /**
   * 更新输出值
   */
  private updateOutputs(): void {
    if (!this.lastResults) {
      this.setOutputValue('isDetected', false);
      return;
    }

    const results = this.lastResults;

    // 基本输出
    this.setOutputValue('landmarks', results.landmarks || []);
    this.setOutputValue('worldLandmarks', results.worldLandmarks || []);
    this.setOutputValue('segmentationMask', results.segmentationMask);
    this.setOutputValue('confidence', results.confidence);
    this.setOutputValue('isDetected', results.confidence > this.config.minDetectionConfidence);

    // 统计信息
    const successRate = this.processingCount > 0 ? this.successCount / this.processingCount : 0;
    this.setOutputValue('successRate', successRate);

    // 特定关键点输出 (MediaPipe Pose关键点索引)
    if (results.landmarks && results.landmarks.length >= 33) {
      const landmarks = results.landmarks;
      
      this.setOutputValue('nose', landmarks[0]);
      this.setOutputValue('leftEye', landmarks[2]);
      this.setOutputValue('rightEye', landmarks[5]);
      this.setOutputValue('leftShoulder', landmarks[11]);
      this.setOutputValue('rightShoulder', landmarks[12]);
      this.setOutputValue('leftElbow', landmarks[13]);
      this.setOutputValue('rightElbow', landmarks[14]);
      this.setOutputValue('leftWrist', landmarks[15]);
      this.setOutputValue('rightWrist', landmarks[16]);
      this.setOutputValue('leftHip', landmarks[23]);
      this.setOutputValue('rightHip', landmarks[24]);
      this.setOutputValue('leftKnee', landmarks[25]);
      this.setOutputValue('rightKnee', landmarks[26]);
      this.setOutputValue('leftAnkle', landmarks[27]);
      this.setOutputValue('rightAnkle', landmarks[28]);
    }
  }

  /**
   * 初始化节点
   */
  public async initialize(): Promise<void> {
    await super.initialize();

    // 如果配置为自动初始化，则初始化检测器
    if (this.config.autoInitialize) {
      await this.initializeDetector();
    }
  }

  /**
   * 销毁节点
   */
  public async destroy(): Promise<void> {
    if (this.poseDetector) {
      this.poseDetector.destroy();
      this.poseDetector = null;
    }

    this.isInitialized = false;
    this.lastResults = null;

    await super.destroy();
  }

  /**
   * 获取节点配置
   */
  public getConfig(): PoseDetectionNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新节点配置
   */
  public updateConfig(newConfig: Partial<PoseDetectionNodeConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 如果检测器已初始化，更新其配置
    if (this.poseDetector && this.isInitialized) {
      this.poseDetector.updateConfig({
        modelComplexity: this.config.modelComplexity,
        smoothLandmarks: this.config.smoothLandmarks,
        enableSegmentation: this.config.enableSegmentation,
        minDetectionConfidence: this.config.minDetectionConfidence,
        minTrackingConfidence: this.config.minTrackingConfidence,
        enableWorldLandmarks: this.config.enableWorldLandmarks
      });
    }
  }

  /**
   * 序列化节点数据
   */
  public serialize(): any {
    return {
      ...super.serialize(),
      config: this.config
    };
  }

  /**
   * 反序列化节点数据
   */
  public deserialize(data: any): void {
    super.deserialize(data);
    if (data.config) {
      this.config = { ...this.config, ...data.config };
    }
  }

  /**
   * 获取节点属性配置
   */
  public getPropertyConfig(): any {
    return {
      modelComplexity: {
        type: 'slider',
        label: '模型复杂度',
        description: '模型复杂度，越高精度越好但速度越慢',
        min: 0,
        max: 2,
        step: 1,
        default: this.config.modelComplexity
      },
      smoothLandmarks: {
        type: 'boolean',
        label: '启用平滑',
        description: '启用关键点平滑处理',
        default: this.config.smoothLandmarks
      },
      enableSegmentation: {
        type: 'boolean',
        label: '启用分割',
        description: '启用人体分割掩码',
        default: this.config.enableSegmentation
      },
      minDetectionConfidence: {
        type: 'slider',
        label: '检测置信度',
        description: '最小检测置信度阈值',
        min: 0,
        max: 1,
        step: 0.1,
        default: this.config.minDetectionConfidence
      },
      minTrackingConfidence: {
        type: 'slider',
        label: '跟踪置信度',
        description: '最小跟踪置信度阈值',
        min: 0,
        max: 1,
        step: 0.1,
        default: this.config.minTrackingConfidence
      },
      enableWorldLandmarks: {
        type: 'boolean',
        label: '启用3D坐标',
        description: '启用世界坐标系关键点',
        default: this.config.enableWorldLandmarks
      },
      autoInitialize: {
        type: 'boolean',
        label: '自动初始化',
        description: '节点创建时自动初始化检测器',
        default: this.config.autoInitialize
      }
    };
  }

  /**
   * 获取最后检测结果
   */
  public getLastResults(): PoseResults | null {
    return this.lastResults;
  }

  /**
   * 获取检测成功率
   */
  public getSuccessRate(): number {
    return this.processingCount > 0 ? this.successCount / this.processingCount : 0;
  }

  /**
   * 是否已初始化
   */
  public get initialized(): boolean {
    return this.isInitialized;
  }
}
