/**
 * 虚拟交互节点
 * 将检测到的手势和动作映射到虚拟环境中的交互
 */
import { VisualScriptNode } from '../VisualScriptNode';
import { NodeCategory } from '../NodeCategory';
import { DataType } from '../DataType';
import { NodeInput, NodeOutput } from '../NodePort';
import { GestureResult, GestureType, InteractionAction } from '../../mocap/interaction/ActionMappingSystem';
import { Debug } from '../../utils/Debug';
import { Vector3 } from 'three';
import type { Entity } from '../../core/Entity';

/**
 * 虚拟交互节点配置
 */
export interface VirtualInteractionNodeConfig {
  /** 是否启用物体交互 */
  enableObjectInteraction: boolean;
  /** 是否启用手势命令 */
  enableGestureCommands: boolean;
  /** 交互距离阈值 */
  interactionDistance: number;
  /** 抓取阈值 */
  grabThreshold: number;
  /** 释放阈值 */
  releaseThreshold: number;
  /** 手势置信度阈值 */
  gestureConfidenceThreshold: number;
  /** 是否启用调试模式 */
  debug: boolean;
}

/**
 * 交互事件数据
 */
export interface InteractionEventData {
  action: InteractionAction;
  targetEntity?: Entity;
  position: Vector3;
  gesture?: GestureResult;
  confidence: number;
  timestamp: number;
}

/**
 * 虚拟交互节点
 */
export class VirtualInteractionNode extends VisualScriptNode {
  /** 节点类型 */
  public static readonly TYPE = 'VirtualInteraction';
  
  /** 节点名称 */
  public static readonly NAME = '虚拟交互';
  
  /** 节点描述 */
  public static readonly DESCRIPTION = '将手势和动作映射到虚拟环境交互';
  
  /** 节点分类 */
  public static readonly CATEGORY = NodeCategory.INTERACTION;

  private config: VirtualInteractionNodeConfig;
  private lastGestures: { left?: GestureResult; right?: GestureResult } = {};
  private grabbedObjects: Map<string, Entity> = new Map(); // hand -> grabbed object
  private interactionHistory: InteractionEventData[] = [];

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: VirtualInteractionNodeConfig = {
    enableObjectInteraction: true,
    enableGestureCommands: true,
    interactionDistance: 2.0,
    grabThreshold: 0.7,
    releaseThreshold: 0.3,
    gestureConfidenceThreshold: 0.6,
    debug: false
  };

  constructor(id: string, config: Partial<VirtualInteractionNodeConfig> = {}) {
    super(id, VirtualInteractionNode.TYPE, VirtualInteractionNode.NAME);
    
    this.config = { ...VirtualInteractionNode.DEFAULT_CONFIG, ...config };
    this.description = VirtualInteractionNode.DESCRIPTION;
    this.category = VirtualInteractionNode.CATEGORY;

    this.setupPorts();
  }

  /**
   * 设置输入输出端口
   */
  private setupPorts(): void {
    // 输入端口
    this.addInput(new NodeInput('leftGesture', '左手手势', DataType.OBJECT, '左手手势识别结果'));
    this.addInput(new NodeInput('rightGesture', '右手手势', DataType.OBJECT, '右手手势识别结果'));
    this.addInput(new NodeInput('leftHandPosition', '左手位置', DataType.VECTOR3, '左手位置'));
    this.addInput(new NodeInput('rightHandPosition', '右手位置', DataType.VECTOR3, '右手位置'));
    this.addInput(new NodeInput('avatarEntity', '化身实体', DataType.OBJECT, '虚拟化身实体'));
    this.addInput(new NodeInput('targetObjects', '目标物体', DataType.ARRAY, '可交互的目标物体数组'));
    this.addInput(new NodeInput('process', '处理', DataType.TRIGGER, '触发交互处理'));

    // 配置输入端口
    this.addInput(new NodeInput('interactionDistance', '交互距离', DataType.NUMBER, '交互距离阈值'));
    this.addInput(new NodeInput('grabThreshold', '抓取阈值', DataType.NUMBER, '抓取手势阈值'));
    this.addInput(new NodeInput('enableObjectInteraction', '启用物体交互', DataType.BOOLEAN, '是否启用物体交互'));

    // 输出端口
    this.addOutput(new NodeOutput('grabbedObject', '被抓取物体', DataType.OBJECT, '当前被抓取的物体'));
    this.addOutput(new NodeOutput('interactionAction', '交互动作', DataType.STRING, '当前交互动作类型'));
    this.addOutput(new NodeOutput('interactionTarget', '交互目标', DataType.OBJECT, '交互目标物体'));
    this.addOutput(new NodeOutput('interactionPosition', '交互位置', DataType.VECTOR3, '交互发生位置'));
    this.addOutput(new NodeOutput('isGrabbing', '正在抓取', DataType.BOOLEAN, '是否正在抓取物体'));
    this.addOutput(new NodeOutput('leftHandGrabbing', '左手抓取', DataType.BOOLEAN, '左手是否在抓取'));
    this.addOutput(new NodeOutput('rightHandGrabbing', '右手抓取', DataType.BOOLEAN, '右手是否在抓取'));

    // 事件输出端口
    this.addOutput(new NodeOutput('onGrab', '抓取事件', DataType.TRIGGER, '抓取物体时触发'));
    this.addOutput(new NodeOutput('onRelease', '释放事件', DataType.TRIGGER, '释放物体时触发'));
    this.addOutput(new NodeOutput('onGestureCommand', '手势命令', DataType.TRIGGER, '识别到手势命令时触发'));
    this.addOutput(new NodeOutput('onInteraction', '交互事件', DataType.TRIGGER, '发生交互时触发'));

    // 手势输出端口
    this.addOutput(new NodeOutput('gestureType', '手势类型', DataType.STRING, '当前手势类型'));
    this.addOutput(new NodeOutput('gestureConfidence', '手势置信度', DataType.NUMBER, '手势识别置信度'));
    this.addOutput(new NodeOutput('gestureHand', '手势手部', DataType.STRING, '执行手势的手部'));
  }

  /**
   * 执行节点
   */
  public async execute(): Promise<void> {
    try {
      // 获取输入
      const leftGesture = this.getInputValue('leftGesture') as GestureResult;
      const rightGesture = this.getInputValue('rightGesture') as GestureResult;
      const leftHandPosition = this.getInputValue('leftHandPosition') as Vector3;
      const rightHandPosition = this.getInputValue('rightHandPosition') as Vector3;
      const avatarEntity = this.getInputValue('avatarEntity') as Entity;
      const targetObjects = this.getInputValue('targetObjects') as Entity[];
      const processTrigger = this.getInputValue('process');

      // 更新配置
      const interactionDistance = this.getInputValue('interactionDistance') as number;
      const grabThreshold = this.getInputValue('grabThreshold') as number;
      const enableObjectInteraction = this.getInputValue('enableObjectInteraction') as boolean;

      if (interactionDistance !== undefined) {
        this.config.interactionDistance = interactionDistance;
      }
      if (grabThreshold !== undefined) {
        this.config.grabThreshold = grabThreshold;
      }
      if (enableObjectInteraction !== undefined) {
        this.config.enableObjectInteraction = enableObjectInteraction;
      }

      // 处理交互
      if (processTrigger && avatarEntity) {
        await this.processInteractions(
          leftGesture,
          rightGesture,
          leftHandPosition,
          rightHandPosition,
          avatarEntity,
          targetObjects || []
        );
      }

      // 更新输出
      this.updateOutputs();

    } catch (error) {
      Debug.error('VirtualInteractionNode', '节点执行失败', error);
    }
  }

  /**
   * 处理交互
   */
  private async processInteractions(
    leftGesture: GestureResult,
    rightGesture: GestureResult,
    leftHandPosition: Vector3,
    rightHandPosition: Vector3,
    avatarEntity: Entity,
    targetObjects: Entity[]
  ): Promise<void> {
    // 处理左手交互
    if (leftGesture && leftHandPosition) {
      await this.processHandInteraction(
        'left',
        leftGesture,
        leftHandPosition,
        avatarEntity,
        targetObjects
      );
    }

    // 处理右手交互
    if (rightGesture && rightHandPosition) {
      await this.processHandInteraction(
        'right',
        rightGesture,
        rightHandPosition,
        avatarEntity,
        targetObjects
      );
    }

    // 检查手势变化
    this.checkGestureChanges(leftGesture, rightGesture);
  }

  /**
   * 处理单手交互
   */
  private async processHandInteraction(
    hand: 'left' | 'right',
    gesture: GestureResult,
    handPosition: Vector3,
    avatarEntity: Entity,
    targetObjects: Entity[]
  ): Promise<void> {
    const lastGesture = this.lastGestures[hand];
    const handKey = hand;

    // 检测手势变化
    if (!lastGesture || lastGesture.type !== gesture.type) {
      await this.handleGestureChange(hand, gesture, lastGesture, handPosition, avatarEntity, targetObjects);
    }

    // 处理持续交互
    if (gesture.type === GestureType.GRAB && this.grabbedObjects.has(handKey)) {
      await this.handleContinuousGrab(hand, handPosition, avatarEntity);
    }

    // 更新手势状态
    this.lastGestures[hand] = gesture;
  }

  /**
   * 处理手势变化
   */
  private async handleGestureChange(
    hand: 'left' | 'right',
    newGesture: GestureResult,
    lastGesture: GestureResult | undefined,
    handPosition: Vector3,
    avatarEntity: Entity,
    targetObjects: Entity[]
  ): Promise<void> {
    const handKey = hand;

    // 处理抓取手势
    if (newGesture.type === GestureType.GRAB && 
        newGesture.confidence > this.config.grabThreshold &&
        (!lastGesture || lastGesture.type !== GestureType.GRAB)) {
      
      await this.handleGrabGesture(hand, handPosition, avatarEntity, targetObjects);
    }

    // 处理释放手势
    if (newGesture.type === GestureType.OPEN_HAND && 
        lastGesture?.type === GestureType.GRAB &&
        this.grabbedObjects.has(handKey)) {
      
      await this.handleReleaseGesture(hand, handPosition, avatarEntity);
    }

    // 处理手势命令
    if (this.config.enableGestureCommands) {
      await this.handleGestureCommand(newGesture, handPosition, avatarEntity);
    }

    // 记录交互事件
    const interactionEvent: InteractionEventData = {
      action: this.gestureToAction(newGesture.type),
      position: handPosition,
      gesture: newGesture,
      confidence: newGesture.confidence,
      timestamp: Date.now()
    };

    this.interactionHistory.push(interactionEvent);
    if (this.interactionHistory.length > 100) {
      this.interactionHistory.shift(); // 保持历史记录在合理范围内
    }

    // 触发交互事件
    this.setOutputValue('onInteraction', true);
    this.triggerOutput('onInteraction');

    if (this.config.debug) {
      Debug.log('VirtualInteractionNode', `手势变化: ${hand} ${lastGesture?.type || 'none'} -> ${newGesture.type}`, {
        confidence: newGesture.confidence,
        position: handPosition
      });
    }
  }

  /**
   * 处理抓取手势
   */
  private async handleGrabGesture(
    hand: 'left' | 'right',
    handPosition: Vector3,
    avatarEntity: Entity,
    targetObjects: Entity[]
  ): Promise<void> {
    if (!this.config.enableObjectInteraction) {
      return;
    }

    // 查找附近的可抓取物体
    const nearbyObjects = this.findNearbyObjects(handPosition, targetObjects);
    
    if (nearbyObjects.length > 0) {
      const targetObject = nearbyObjects[0];
      const handKey = hand;

      // 检查是否已经被抓取
      if (!this.grabbedObjects.has(handKey)) {
        // 执行抓取
        const grabberComponent = avatarEntity.getComponent('GrabberComponent');
        if (grabberComponent) {
          const handEnum = hand === 'left' ? 0 : 1; // 假设Hand枚举值
          const success = grabberComponent.grab(targetObject, handEnum);
          
          if (success) {
            this.grabbedObjects.set(handKey, targetObject);
            
            // 触发抓取事件
            this.setOutputValue('onGrab', true);
            this.triggerOutput('onGrab');

            if (this.config.debug) {
              Debug.log('VirtualInteractionNode', `${hand}手抓取物体`, {
                objectId: targetObject.id,
                position: handPosition
              });
            }
          }
        }
      }
    }
  }

  /**
   * 处理释放手势
   */
  private async handleReleaseGesture(
    hand: 'left' | 'right',
    handPosition: Vector3,
    avatarEntity: Entity
  ): Promise<void> {
    const handKey = hand;
    const grabbedObject = this.grabbedObjects.get(handKey);

    if (grabbedObject) {
      // 执行释放
      const grabberComponent = avatarEntity.getComponent('GrabberComponent');
      if (grabberComponent) {
        const handEnum = hand === 'left' ? 0 : 1; // 假设Hand枚举值
        const releasedObject = grabberComponent.release(handEnum);
        
        if (releasedObject) {
          this.grabbedObjects.delete(handKey);
          
          // 触发释放事件
          this.setOutputValue('onRelease', true);
          this.triggerOutput('onRelease');

          if (this.config.debug) {
            Debug.log('VirtualInteractionNode', `${hand}手释放物体`, {
              objectId: releasedObject.id,
              position: handPosition
            });
          }
        }
      }
    }
  }

  /**
   * 处理持续抓取
   */
  private async handleContinuousGrab(
    hand: 'left' | 'right',
    handPosition: Vector3,
    avatarEntity: Entity
  ): Promise<void> {
    const handKey = hand;
    const grabbedObject = this.grabbedObjects.get(handKey);

    if (grabbedObject) {
      // 更新被抓取物体的位置
      const transform = grabbedObject.getComponent('Transform');
      if (transform) {
        // 将手部位置转换为世界坐标
        const worldPosition = this.handPositionToWorldPosition(avatarEntity, handPosition);
        transform.position.copy(worldPosition);
      }
    }
  }

  /**
   * 处理手势命令
   */
  private async handleGestureCommand(
    gesture: GestureResult,
    handPosition: Vector3,
    avatarEntity: Entity
  ): Promise<void> {
    // 处理特殊手势命令
    switch (gesture.type) {
      case GestureType.THUMBS_UP:
        // 竖拇指手势 - 可以用作确认命令
        this.setOutputValue('onGestureCommand', true);
        this.triggerOutput('onGestureCommand');
        break;
        
      case GestureType.PEACE_SIGN:
        // 胜利手势 - 可以用作特殊命令
        this.setOutputValue('onGestureCommand', true);
        this.triggerOutput('onGestureCommand');
        break;
        
      case GestureType.POINTING:
        // 指向手势 - 可以用作选择命令
        this.setOutputValue('onGestureCommand', true);
        this.triggerOutput('onGestureCommand');
        break;
    }
  }

  /**
   * 检查手势变化
   */
  private checkGestureChanges(leftGesture: GestureResult, rightGesture: GestureResult): void {
    let gestureChanged = false;

    // 检查左手手势变化
    if (leftGesture && (!this.lastGestures.left || this.lastGestures.left.type !== leftGesture.type)) {
      gestureChanged = true;
    }

    // 检查右手手势变化
    if (rightGesture && (!this.lastGestures.right || this.lastGestures.right.type !== rightGesture.type)) {
      gestureChanged = true;
    }

    if (gestureChanged) {
      // 输出当前手势信息
      const currentGesture = rightGesture || leftGesture;
      if (currentGesture) {
        this.setOutputValue('gestureType', currentGesture.type);
        this.setOutputValue('gestureConfidence', currentGesture.confidence);
        this.setOutputValue('gestureHand', currentGesture.hand);
      }
    }
  }

  /**
   * 查找附近的物体
   */
  private findNearbyObjects(position: Vector3, targetObjects: Entity[]): Entity[] {
    const nearbyObjects: Entity[] = [];

    for (const entity of targetObjects) {
      const transform = entity.getComponent('Transform');
      const grabbableComponent = entity.getComponent('GrabbableComponent');

      if (transform && grabbableComponent) {
        const distance = position.distanceTo(transform.position);
        
        if (distance <= this.config.interactionDistance) {
          nearbyObjects.push(entity);
        }
      }
    }

    // 按距离排序
    nearbyObjects.sort((a, b) => {
      const transformA = a.getComponent('Transform');
      const transformB = b.getComponent('Transform');
      
      if (!transformA || !transformB) return 0;
      
      const distanceA = position.distanceTo(transformA.position);
      const distanceB = position.distanceTo(transformB.position);
      
      return distanceA - distanceB;
    });

    return nearbyObjects;
  }

  /**
   * 将手部位置转换为世界坐标
   */
  private handPositionToWorldPosition(avatarEntity: Entity, handPosition: Vector3): Vector3 {
    const entityTransform = avatarEntity.getComponent('Transform');
    if (entityTransform) {
      // 简单的相对位置转换
      return entityTransform.position.clone().add(handPosition);
    }
    
    return handPosition.clone();
  }

  /**
   * 将手势类型转换为交互动作
   */
  private gestureToAction(gestureType: GestureType): InteractionAction {
    switch (gestureType) {
      case GestureType.GRAB:
        return InteractionAction.GRAB_OBJECT;
      case GestureType.OPEN_HAND:
        return InteractionAction.RELEASE_OBJECT;
      case GestureType.POINTING:
        return InteractionAction.POINT_AT_OBJECT;
      case GestureType.THUMBS_UP:
      case GestureType.PEACE_SIGN:
        return InteractionAction.GESTURE_COMMAND;
      default:
        return InteractionAction.WAVE_HAND;
    }
  }

  /**
   * 更新输出值
   */
  private updateOutputs(): void {
    // 抓取状态输出
    const leftGrabbing = this.grabbedObjects.has('left');
    const rightGrabbing = this.grabbedObjects.has('right');
    const isGrabbing = leftGrabbing || rightGrabbing;

    this.setOutputValue('isGrabbing', isGrabbing);
    this.setOutputValue('leftHandGrabbing', leftGrabbing);
    this.setOutputValue('rightHandGrabbing', rightGrabbing);

    // 被抓取物体输出
    const grabbedObject = this.grabbedObjects.get('right') || this.grabbedObjects.get('left');
    this.setOutputValue('grabbedObject', grabbedObject || null);

    // 最近交互信息
    if (this.interactionHistory.length > 0) {
      const lastInteraction = this.interactionHistory[this.interactionHistory.length - 1];
      this.setOutputValue('interactionAction', lastInteraction.action);
      this.setOutputValue('interactionTarget', lastInteraction.targetEntity || null);
      this.setOutputValue('interactionPosition', lastInteraction.position);
    }
  }

  /**
   * 获取节点配置
   */
  public getConfig(): VirtualInteractionNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新节点配置
   */
  public updateConfig(newConfig: Partial<VirtualInteractionNodeConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 序列化节点数据
   */
  public serialize(): any {
    return {
      ...super.serialize(),
      config: this.config
    };
  }

  /**
   * 反序列化节点数据
   */
  public deserialize(data: any): void {
    super.deserialize(data);
    if (data.config) {
      this.config = { ...this.config, ...data.config };
    }
  }

  /**
   * 获取节点属性配置
   */
  public getPropertyConfig(): any {
    return {
      enableObjectInteraction: {
        type: 'boolean',
        label: '启用物体交互',
        description: '是否启用物体抓取和释放交互',
        default: this.config.enableObjectInteraction
      },
      enableGestureCommands: {
        type: 'boolean',
        label: '启用手势命令',
        description: '是否启用手势命令识别',
        default: this.config.enableGestureCommands
      },
      interactionDistance: {
        type: 'slider',
        label: '交互距离',
        description: '手部与物体的最大交互距离',
        min: 0.5,
        max: 5.0,
        step: 0.1,
        default: this.config.interactionDistance
      },
      grabThreshold: {
        type: 'slider',
        label: '抓取阈值',
        description: '抓取手势的置信度阈值',
        min: 0,
        max: 1,
        step: 0.1,
        default: this.config.grabThreshold
      },
      releaseThreshold: {
        type: 'slider',
        label: '释放阈值',
        description: '释放手势的置信度阈值',
        min: 0,
        max: 1,
        step: 0.1,
        default: this.config.releaseThreshold
      },
      gestureConfidenceThreshold: {
        type: 'slider',
        label: '手势置信度阈值',
        description: '手势识别的最小置信度',
        min: 0,
        max: 1,
        step: 0.1,
        default: this.config.gestureConfidenceThreshold
      },
      debug: {
        type: 'boolean',
        label: '调试模式',
        description: '启用调试日志输出',
        default: this.config.debug
      }
    };
  }

  /**
   * 获取交互历史
   */
  public getInteractionHistory(): InteractionEventData[] {
    return [...this.interactionHistory];
  }

  /**
   * 清除交互历史
   */
  public clearInteractionHistory(): void {
    this.interactionHistory = [];
  }

  /**
   * 获取当前抓取的物体
   */
  public getGrabbedObjects(): Map<string, Entity> {
    return new Map(this.grabbedObjects);
  }
}
