/**
 * 优化的推荐引擎
 * 提供个性化推荐、协同过滤、内容推荐、混合推荐等高级功能
 */
import { System } from '../../core/System';
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';

/**
 * 推荐类型
 */
export enum RecommendationType {
  COLLABORATIVE_FILTERING = 'collaborative_filtering',
  CONTENT_BASED = 'content_based',
  HYBRID = 'hybrid',
  KNOWLEDGE_BASED = 'knowledge_based',
  DEMOGRAPHIC = 'demographic',
  CONTEXT_AWARE = 'context_aware'
}

/**
 * 用户行为类型
 */
export enum UserBehaviorType {
  VIEW = 'view',
  CLICK = 'click',
  LIKE = 'like',
  SHARE = 'share',
  PURCHASE = 'purchase',
  DOWNLOAD = 'download',
  RATE = 'rate',
  COMMENT = 'comment',
  BOOKMARK = 'bookmark',
  SEARCH = 'search'
}

/**
 * 用户画像
 */
export interface UserProfile {
  /** 用户ID */
  userId: string;
  /** 人口统计信息 */
  demographics: {
    age?: number;
    gender?: string;
    location?: string;
    occupation?: string;
    education?: string;
  };
  /** 兴趣标签 */
  interests: string[];
  /** 技能水平 */
  skillLevels: Record<string, number>;
  /** 偏好设置 */
  preferences: {
    categories: string[];
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    contentTypes: string[];
    languages: string[];
  };
  /** 行为特征 */
  behaviorPatterns: {
    activeHours: number[];
    sessionDuration: number;
    interactionFrequency: number;
    explorationTendency: number;
  };
}

/**
 * 用户行为记录
 */
export interface UserBehavior {
  /** 行为ID */
  id: string;
  /** 用户ID */
  userId: string;
  /** 物品ID */
  itemId: string;
  /** 行为类型 */
  type: UserBehaviorType;
  /** 行为值 */
  value: number;
  /** 时间戳 */
  timestamp: number;
  /** 上下文信息 */
  context: {
    device: string;
    location: string;
    sessionId: string;
    referrer?: string;
  };
  /** 隐式反馈 */
  implicitFeedback: {
    duration: number;
    scrollDepth: number;
    clickPosition: { x: number; y: number };
  };
}

/**
 * 物品信息
 */
export interface ItemInfo {
  /** 物品ID */
  id: string;
  /** 物品标题 */
  title: string;
  /** 物品描述 */
  description: string;
  /** 类别 */
  category: string;
  /** 标签 */
  tags: string[];
  /** 特征向量 */
  features: number[];
  /** 元数据 */
  metadata: {
    author: string;
    createdAt: number;
    updatedAt: number;
    difficulty: number;
    popularity: number;
    quality: number;
  };
  /** 统计信息 */
  stats: {
    viewCount: number;
    likeCount: number;
    shareCount: number;
    averageRating: number;
    ratingCount: number;
    popularity: number;
  };
}

/**
 * 推荐结果
 */
export interface RecommendationResult {
  /** 物品ID */
  itemId: string;
  /** 推荐分数 */
  score: number;
  /** 推荐原因 */
  reason: string;
  /** 推荐类型 */
  type: RecommendationType;
  /** 置信度 */
  confidence: number;
  /** 解释 */
  explanation: string;
  /** 多样性分数 */
  diversityScore: number;
}

/**
 * 推荐配置
 */
export interface RecommendationConfig {
  /** 推荐算法权重 */
  algorithmWeights: Record<RecommendationType, number>;
  /** 推荐数量 */
  recommendationCount: number;
  /** 多样性阈值 */
  diversityThreshold: number;
  /** 新颖性权重 */
  noveltyWeight: number;
  /** 流行度权重 */
  popularityWeight: number;
  /** 实时更新 */
  enableRealTimeUpdate: boolean;
  /** 冷启动策略 */
  coldStartStrategy: 'popular' | 'random' | 'demographic' | 'content';
  /** 缓存配置 */
  cache: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
  };
}

/**
 * 推荐上下文
 */
export interface RecommendationContext {
  /** 当前时间 */
  timestamp: number;
  /** 设备信息 */
  device: string;
  /** 位置信息 */
  location: string;
  /** 会话信息 */
  session: {
    id: string;
    duration: number;
    pageViews: number;
  };
  /** 当前页面 */
  currentPage: string;
  /** 推荐场景 */
  scenario: 'homepage' | 'detail' | 'search' | 'category' | 'profile';
}

/**
 * 优化的推荐引擎
 */
export class OptimizedRecommendationEngine extends System {
  public static readonly TYPE = 'OptimizedRecommendationEngine';

  private config: RecommendationConfig;
  private eventEmitter: EventEmitter;
  private performanceMonitor: PerformanceMonitor;
  private userProfiles: Map<string, UserProfile>;
  private userBehaviors: Map<string, UserBehavior[]>;
  private itemCatalog: Map<string, ItemInfo>;
  private userItemMatrix: Map<string, Map<string, number>>;
  private itemSimilarityMatrix: Map<string, Map<string, number>>;
  private userSimilarityMatrix: Map<string, Map<string, number>>;
  private recommendationCache: Map<string, RecommendationResult[]>;
  private modelUpdateTime: number;
  private updateTimer?: NodeJS.Timeout;

  constructor(config: RecommendationConfig) {
    super();
    this.config = config;
    this.eventEmitter = new EventEmitter();
    this.performanceMonitor = PerformanceMonitor.getInstance();
    this.userProfiles = new Map();
    this.userBehaviors = new Map();
    this.itemCatalog = new Map();
    this.userItemMatrix = new Map();
    this.itemSimilarityMatrix = new Map();
    this.userSimilarityMatrix = new Map();
    this.recommendationCache = new Map();
    this.modelUpdateTime = 0;

    this.initializeEngine();
  }

  /**
   * 初始化推荐引擎
   */
  private initializeEngine(): void {
    this.performanceMonitor.start();
    
    // 启动模型更新定时器
    if (this.config.enableRealTimeUpdate) {
      this.startModelUpdateTimer();
    }
    
    Debug.log('优化推荐引擎初始化完成');
  }

  /**
   * 添加用户画像
   */
  public addUserProfile(profile: UserProfile): void {
    this.userProfiles.set(profile.userId, profile);
    Debug.log(`添加用户画像: ${profile.userId}`);
  }

  /**
   * 记录用户行为
   */
  public recordUserBehavior(behavior: UserBehavior): void {
    if (!this.userBehaviors.has(behavior.userId)) {
      this.userBehaviors.set(behavior.userId, []);
    }
    
    this.userBehaviors.get(behavior.userId)!.push(behavior);
    
    // 更新用户-物品矩阵
    this.updateUserItemMatrix(behavior);
    
    // 触发实时更新
    if (this.config.enableRealTimeUpdate) {
      this.updateModelsIncremental(behavior);
    }
    
    this.eventEmitter.emit('behaviorRecorded', behavior);
  }

  /**
   * 添加物品信息
   */
  public addItem(item: ItemInfo): void {
    this.itemCatalog.set(item.id, item);
    Debug.log(`添加物品: ${item.id}`);
  }

  /**
   * 获取推荐
   */
  public async getRecommendations(
    userId: string,
    context: RecommendationContext,
    count?: number
  ): Promise<RecommendationResult[]> {
    const startTime = performance.now();
    const recommendationCount = count || this.config.recommendationCount;

    // 检查缓存
    const cacheKey = this.generateCacheKey(userId, context);
    if (this.config.cache.enabled && this.recommendationCache.has(cacheKey)) {
      const cached = this.recommendationCache.get(cacheKey)!;
      Debug.log(`使用缓存推荐: ${userId}`);
      return cached.slice(0, recommendationCount);
    }

    // 获取用户画像
    const userProfile = this.userProfiles.get(userId);
    if (!userProfile) {
      return this.getColdStartRecommendations(userId, context, recommendationCount);
    }

    // 多算法推荐
    const recommendations = new Map<string, RecommendationResult>();

    // 协同过滤推荐
    if (this.config.algorithmWeights[RecommendationType.COLLABORATIVE_FILTERING] > 0) {
      const cfRecommendations = await this.getCollaborativeFilteringRecommendations(userId, userProfile);
      this.mergeRecommendations(recommendations, cfRecommendations, RecommendationType.COLLABORATIVE_FILTERING);
    }

    // 基于内容的推荐
    if (this.config.algorithmWeights[RecommendationType.CONTENT_BASED] > 0) {
      const cbRecommendations = await this.getContentBasedRecommendations(userId, userProfile);
      this.mergeRecommendations(recommendations, cbRecommendations, RecommendationType.CONTENT_BASED);
    }

    // 基于知识的推荐
    if (this.config.algorithmWeights[RecommendationType.KNOWLEDGE_BASED] > 0) {
      const kbRecommendations = await this.getKnowledgeBasedRecommendations(userId, userProfile, context);
      this.mergeRecommendations(recommendations, kbRecommendations, RecommendationType.KNOWLEDGE_BASED);
    }

    // 上下文感知推荐
    if (this.config.algorithmWeights[RecommendationType.CONTEXT_AWARE] > 0) {
      const caRecommendations = await this.getContextAwareRecommendations(userId, userProfile, context);
      this.mergeRecommendations(recommendations, caRecommendations, RecommendationType.CONTEXT_AWARE);
    }

    // 排序和过滤
    let finalRecommendations = Array.from(recommendations.values())
      .sort((a, b) => b.score - a.score);

    // 应用多样性过滤
    finalRecommendations = this.applyDiversityFilter(finalRecommendations);

    // 应用新颖性和流行度调整
    finalRecommendations = this.applyNoveltyAndPopularityAdjustment(finalRecommendations, userId);

    // 限制数量
    finalRecommendations = finalRecommendations.slice(0, recommendationCount);

    // 缓存结果
    if (this.config.cache.enabled) {
      this.cacheRecommendations(cacheKey, finalRecommendations);
    }

    const endTime = performance.now();
    Debug.log(`推荐生成完成，耗时: ${(endTime - startTime).toFixed(2)}ms，用户: ${userId}`);

    return finalRecommendations;
  }

  /**
   * 协同过滤推荐
   */
  private async getCollaborativeFilteringRecommendations(
    userId: string,
    userProfile: UserProfile
  ): Promise<RecommendationResult[]> {
    const recommendations: RecommendationResult[] = [];
    
    // 找到相似用户
    const similarUsers = this.findSimilarUsers(userId, 10);
    
    // 基于相似用户的偏好推荐物品
    const itemScores = new Map<string, number>();
    
    for (const [similarUserId, similarity] of similarUsers) {
      const similarUserBehaviors = this.userBehaviors.get(similarUserId) || [];
      
      for (const behavior of similarUserBehaviors) {
        if (behavior.type === UserBehaviorType.LIKE || behavior.type === UserBehaviorType.RATE) {
          const currentScore = itemScores.get(behavior.itemId) || 0;
          itemScores.set(behavior.itemId, currentScore + behavior.value * similarity);
        }
      }
    }

    // 过滤用户已交互的物品
    const userInteractedItems = new Set(
      (this.userBehaviors.get(userId) || []).map(b => b.itemId)
    );

    for (const [itemId, score] of itemScores.entries()) {
      if (!userInteractedItems.has(itemId)) {
        recommendations.push({
          itemId,
          score,
          reason: '基于相似用户的偏好',
          type: RecommendationType.COLLABORATIVE_FILTERING,
          confidence: Math.min(score / 10, 1),
          explanation: `与您兴趣相似的用户也喜欢这个内容`,
          diversityScore: 0
        });
      }
    }

    return recommendations.sort((a, b) => b.score - a.score).slice(0, 20);
  }

  /**
   * 基于内容的推荐
   */
  private async getContentBasedRecommendations(
    userId: string,
    userProfile: UserProfile
  ): Promise<RecommendationResult[]> {
    const recommendations: RecommendationResult[] = [];
    
    // 获取用户历史偏好
    const userPreferences = this.buildUserPreferenceVector(userId);
    
    // 计算物品相似度
    for (const [itemId, item] of this.itemCatalog.entries()) {
      const similarity = this.calculateContentSimilarity(userPreferences, item.features);
      
      if (similarity > 0.1) {
        recommendations.push({
          itemId,
          score: similarity,
          reason: '基于内容相似性',
          type: RecommendationType.CONTENT_BASED,
          confidence: similarity,
          explanation: `与您之前喜欢的内容相似`,
          diversityScore: 0
        });
      }
    }

    return recommendations.sort((a, b) => b.score - a.score).slice(0, 20);
  }

  /**
   * 基于知识的推荐
   */
  private async getKnowledgeBasedRecommendations(
    userId: string,
    userProfile: UserProfile,
    context: RecommendationContext
  ): Promise<RecommendationResult[]> {
    const recommendations: RecommendationResult[] = [];
    
    // 基于用户技能水平推荐
    for (const [itemId, item] of this.itemCatalog.entries()) {
      let score = 0;
      
      // 技能匹配
      const userSkillLevel = userProfile.skillLevels[item.category] || 0;
      const itemDifficulty = item.metadata.difficulty;
      
      if (Math.abs(userSkillLevel - itemDifficulty) < 0.3) {
        score += 0.8;
      }
      
      // 兴趣匹配
      const interestMatch = userProfile.interests.some(interest => 
        item.tags.includes(interest) || item.category === interest
      );
      
      if (interestMatch) {
        score += 0.6;
      }
      
      // 偏好匹配
      if (userProfile.preferences.categories.includes(item.category)) {
        score += 0.4;
      }

      if (score > 0.5) {
        recommendations.push({
          itemId,
          score,
          reason: '基于知识规则',
          type: RecommendationType.KNOWLEDGE_BASED,
          confidence: score,
          explanation: `符合您的技能水平和兴趣偏好`,
          diversityScore: 0
        });
      }
    }

    return recommendations.sort((a, b) => b.score - a.score).slice(0, 20);
  }

  /**
   * 上下文感知推荐
   */
  private async getContextAwareRecommendations(
    userId: string,
    userProfile: UserProfile,
    context: RecommendationContext
  ): Promise<RecommendationResult[]> {
    const recommendations: RecommendationResult[] = [];
    
    // 基于时间上下文
    const hour = new Date(context.timestamp).getHours();
    const isActiveHour = userProfile.behaviorPatterns.activeHours.includes(hour);
    
    // 基于设备上下文
    const isMobile = context.device === 'mobile';
    
    // 基于场景上下文
    const scenario = context.scenario;

    for (const [itemId, item] of this.itemCatalog.entries()) {
      let score = 0;
      
      // 时间因子
      if (isActiveHour) {
        score += 0.3;
      }
      
      // 设备因子
      if (isMobile && item.metadata.difficulty < 0.5) {
        score += 0.2; // 移动设备偏好简单内容
      }
      
      // 场景因子
      switch (scenario) {
        case 'homepage':
          score += item.stats.popularity * 0.4;
          break;
        case 'detail':
          // 详情页推荐相关内容
          score += 0.5;
          break;
        case 'search':
          // 搜索页推荐热门内容
          score += item.stats.averageRating * 0.3;
          break;
      }

      if (score > 0.2) {
        recommendations.push({
          itemId,
          score,
          reason: '基于上下文',
          type: RecommendationType.CONTEXT_AWARE,
          confidence: score,
          explanation: `适合当前的使用场景`,
          diversityScore: 0
        });
      }
    }

    return recommendations.sort((a, b) => b.score - a.score).slice(0, 20);
  }

  /**
   * 冷启动推荐
   */
  private getColdStartRecommendations(
    userId: string,
    context: RecommendationContext,
    count: number
  ): RecommendationResult[] {
    const recommendations: RecommendationResult[] = [];
    
    switch (this.config.coldStartStrategy) {
      case 'popular':
        // 推荐热门内容
        const popularItems = Array.from(this.itemCatalog.values())
          .sort((a, b) => b.stats.popularity - a.stats.popularity)
          .slice(0, count);
        
        popularItems.forEach(item => {
          recommendations.push({
            itemId: item.id,
            score: item.stats.popularity,
            reason: '热门推荐',
            type: RecommendationType.DEMOGRAPHIC,
            confidence: 0.6,
            explanation: '这些是最受欢迎的内容',
            diversityScore: 0
          });
        });
        break;
        
      case 'random':
        // 随机推荐
        const allItems = Array.from(this.itemCatalog.values());
        const randomItems = this.shuffleArray(allItems).slice(0, count);
        
        randomItems.forEach(item => {
          recommendations.push({
            itemId: item.id,
            score: Math.random(),
            reason: '随机推荐',
            type: RecommendationType.DEMOGRAPHIC,
            confidence: 0.3,
            explanation: '为您随机推荐的内容',
            diversityScore: 1
          });
        });
        break;
    }

    return recommendations;
  }

  /**
   * 合并推荐结果
   */
  private mergeRecommendations(
    target: Map<string, RecommendationResult>,
    source: RecommendationResult[],
    type: RecommendationType
  ): void {
    const weight = this.config.algorithmWeights[type];

    for (const recommendation of source) {
      const existing = target.get(recommendation.itemId);

      if (existing) {
        // 加权平均
        existing.score = (existing.score + recommendation.score * weight) / 2;
        existing.confidence = Math.max(existing.confidence, recommendation.confidence);
      } else {
        target.set(recommendation.itemId, {
          ...recommendation,
          score: recommendation.score * weight
        });
      }
    }
  }

  /**
   * 应用多样性过滤
   */
  private applyDiversityFilter(recommendations: RecommendationResult[]): RecommendationResult[] {
    if (recommendations.length <= 1) return recommendations;

    const filtered: RecommendationResult[] = [recommendations[0]];

    for (let i = 1; i < recommendations.length; i++) {
      const candidate = recommendations[i];
      let minSimilarity = 1;

      // 计算与已选择物品的最小相似度
      for (const selected of filtered) {
        const similarity = this.calculateItemSimilarity(candidate.itemId, selected.itemId);
        minSimilarity = Math.min(minSimilarity, similarity);
      }

      // 多样性分数
      candidate.diversityScore = 1 - minSimilarity;

      // 如果多样性足够，添加到结果中
      if (candidate.diversityScore >= this.config.diversityThreshold) {
        filtered.push(candidate);
      }
    }

    return filtered;
  }

  /**
   * 应用新颖性和流行度调整
   */
  private applyNoveltyAndPopularityAdjustment(
    recommendations: RecommendationResult[],
    userId: string
  ): RecommendationResult[] {
    const userBehaviors = this.userBehaviors.get(userId) || [];
    const userInteractedCategories = new Set(
      userBehaviors.map(b => this.itemCatalog.get(b.itemId)?.category).filter(Boolean)
    );

    return recommendations.map(rec => {
      const item = this.itemCatalog.get(rec.itemId);
      if (!item) return rec;

      // 新颖性调整
      const isNovelCategory = !userInteractedCategories.has(item.category);
      const noveltyBonus = isNovelCategory ? this.config.noveltyWeight : 0;

      // 流行度调整
      const popularityBonus = item.stats.popularity * this.config.popularityWeight;

      return {
        ...rec,
        score: rec.score + noveltyBonus + popularityBonus
      };
    });
  }

  /**
   * 更新用户-物品矩阵
   */
  private updateUserItemMatrix(behavior: UserBehavior): void {
    if (!this.userItemMatrix.has(behavior.userId)) {
      this.userItemMatrix.set(behavior.userId, new Map());
    }

    const userMatrix = this.userItemMatrix.get(behavior.userId)!;
    const currentRating = userMatrix.get(behavior.itemId) || 0;

    // 根据行为类型计算评分
    let rating = 0;
    switch (behavior.type) {
      case UserBehaviorType.VIEW:
        rating = 1;
        break;
      case UserBehaviorType.LIKE:
        rating = 4;
        break;
      case UserBehaviorType.SHARE:
        rating = 3;
        break;
      case UserBehaviorType.PURCHASE:
        rating = 5;
        break;
      case UserBehaviorType.RATE:
        rating = behavior.value;
        break;
      default:
        rating = 2;
    }

    // 更新评分（加权平均）
    const newRating = currentRating > 0 ? (currentRating + rating) / 2 : rating;
    userMatrix.set(behavior.itemId, newRating);
  }

  /**
   * 增量更新模型
   */
  private updateModelsIncremental(behavior: UserBehavior): void {
    // 更新用户相似度矩阵
    this.updateUserSimilarity(behavior.userId);

    // 更新物品相似度矩阵
    this.updateItemSimilarity(behavior.itemId);

    // 清除相关缓存
    this.clearRelatedCache(behavior.userId);
  }

  /**
   * 查找相似用户
   */
  private findSimilarUsers(userId: string, count: number): Array<[string, number]> {
    const similarities: Array<[string, number]> = [];

    for (const [otherUserId] of this.userItemMatrix.entries()) {
      if (otherUserId !== userId) {
        const similarity = this.calculateUserSimilarity(userId, otherUserId);
        if (similarity > 0.1) {
          similarities.push([otherUserId, similarity]);
        }
      }
    }

    return similarities
      .sort((a, b) => b[1] - a[1])
      .slice(0, count);
  }

  /**
   * 计算用户相似度
   */
  private calculateUserSimilarity(userId1: string, userId2: string): number {
    const user1Matrix = this.userItemMatrix.get(userId1);
    const user2Matrix = this.userItemMatrix.get(userId2);

    if (!user1Matrix || !user2Matrix) return 0;

    // 找到共同评分的物品
    const commonItems: string[] = [];
    for (const itemId of user1Matrix.keys()) {
      if (user2Matrix.has(itemId)) {
        commonItems.push(itemId);
      }
    }

    if (commonItems.length === 0) return 0;

    // 计算皮尔逊相关系数
    let sum1 = 0, sum2 = 0, sum1Sq = 0, sum2Sq = 0, pSum = 0;

    for (const itemId of commonItems) {
      const rating1 = user1Matrix.get(itemId)!;
      const rating2 = user2Matrix.get(itemId)!;

      sum1 += rating1;
      sum2 += rating2;
      sum1Sq += rating1 * rating1;
      sum2Sq += rating2 * rating2;
      pSum += rating1 * rating2;
    }

    const num = pSum - (sum1 * sum2 / commonItems.length);
    const den = Math.sqrt(
      (sum1Sq - sum1 * sum1 / commonItems.length) *
      (sum2Sq - sum2 * sum2 / commonItems.length)
    );

    return den === 0 ? 0 : num / den;
  }

  /**
   * 计算物品相似度
   */
  private calculateItemSimilarity(itemId1: string, itemId2: string): number {
    const item1 = this.itemCatalog.get(itemId1);
    const item2 = this.itemCatalog.get(itemId2);

    if (!item1 || !item2) return 0;

    // 基于特征向量计算余弦相似度
    return this.calculateCosineSimilarity(item1.features, item2.features);
  }

  /**
   * 计算余弦相似度
   */
  private calculateCosineSimilarity(vector1: number[], vector2: number[]): number {
    if (vector1.length !== vector2.length) return 0;

    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i];
      norm1 += vector1[i] * vector1[i];
      norm2 += vector2[i] * vector2[i];
    }

    const denominator = Math.sqrt(norm1) * Math.sqrt(norm2);
    return denominator === 0 ? 0 : dotProduct / denominator;
  }

  /**
   * 构建用户偏好向量
   */
  private buildUserPreferenceVector(userId: string): number[] {
    const behaviors = this.userBehaviors.get(userId) || [];
    const preferenceVector = new Array(100).fill(0); // 假设特征向量长度为100

    for (const behavior of behaviors) {
      const item = this.itemCatalog.get(behavior.itemId);
      if (item && behavior.value > 0) {
        for (let i = 0; i < item.features.length && i < preferenceVector.length; i++) {
          preferenceVector[i] += item.features[i] * behavior.value;
        }
      }
    }

    // 归一化
    const norm = Math.sqrt(preferenceVector.reduce((sum, val) => sum + val * val, 0));
    return norm > 0 ? preferenceVector.map(val => val / norm) : preferenceVector;
  }

  /**
   * 计算内容相似度
   */
  private calculateContentSimilarity(userVector: number[], itemVector: number[]): number {
    return this.calculateCosineSimilarity(userVector, itemVector);
  }

  /**
   * 更新用户相似度
   */
  private updateUserSimilarity(userId: string): void {
    if (!this.userSimilarityMatrix.has(userId)) {
      this.userSimilarityMatrix.set(userId, new Map());
    }

    const userSimilarities = this.userSimilarityMatrix.get(userId)!;

    for (const [otherUserId] of this.userItemMatrix.entries()) {
      if (otherUserId !== userId) {
        const similarity = this.calculateUserSimilarity(userId, otherUserId);
        userSimilarities.set(otherUserId, similarity);
      }
    }
  }

  /**
   * 更新物品相似度
   */
  private updateItemSimilarity(itemId: string): void {
    if (!this.itemSimilarityMatrix.has(itemId)) {
      this.itemSimilarityMatrix.set(itemId, new Map());
    }

    const itemSimilarities = this.itemSimilarityMatrix.get(itemId)!;

    for (const [otherItemId] of this.itemCatalog.entries()) {
      if (otherItemId !== itemId) {
        const similarity = this.calculateItemSimilarity(itemId, otherItemId);
        itemSimilarities.set(otherItemId, similarity);
      }
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(userId: string, context: RecommendationContext): string {
    return `${userId}_${context.scenario}_${context.device}_${Math.floor(context.timestamp / 3600000)}`;
  }

  /**
   * 缓存推荐结果
   */
  private cacheRecommendations(cacheKey: string, recommendations: RecommendationResult[]): void {
    if (this.recommendationCache.size >= this.config.cache.maxSize) {
      // 清理最旧的缓存
      const oldestKey = this.recommendationCache.keys().next().value;
      this.recommendationCache.delete(oldestKey);
    }

    this.recommendationCache.set(cacheKey, recommendations);

    // 设置TTL
    setTimeout(() => {
      this.recommendationCache.delete(cacheKey);
    }, this.config.cache.ttl);
  }

  /**
   * 清除相关缓存
   */
  private clearRelatedCache(userId: string): void {
    for (const [key] of this.recommendationCache.entries()) {
      if (key.startsWith(userId)) {
        this.recommendationCache.delete(key);
      }
    }
  }

  /**
   * 启动模型更新定时器
   */
  private startModelUpdateTimer(): void {
    this.updateTimer = setInterval(() => {
      this.updateAllModels();
    }, 3600000); // 每小时更新一次
  }

  /**
   * 更新所有模型
   */
  private updateAllModels(): void {
    const startTime = performance.now();

    // 重新计算所有相似度矩阵
    this.recalculateAllSimilarities();

    // 清除所有缓存
    this.recommendationCache.clear();

    this.modelUpdateTime = Date.now();

    const endTime = performance.now();
    Debug.log(`模型更新完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);
  }

  /**
   * 重新计算所有相似度
   */
  private recalculateAllSimilarities(): void {
    // 重新计算用户相似度
    for (const userId of this.userItemMatrix.keys()) {
      this.updateUserSimilarity(userId);
    }

    // 重新计算物品相似度
    for (const itemId of this.itemCatalog.keys()) {
      this.updateItemSimilarity(itemId);
    }
  }

  /**
   * 随机打乱数组
   */
  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  /**
   * 获取推荐统计
   */
  public getRecommendationStats(): any {
    return {
      userCount: this.userProfiles.size,
      itemCount: this.itemCatalog.size,
      behaviorCount: Array.from(this.userBehaviors.values()).reduce((sum, behaviors) => sum + behaviors.length, 0),
      cacheSize: this.recommendationCache.size,
      modelUpdateTime: this.modelUpdateTime,
      userSimilarityMatrixSize: this.userSimilarityMatrix.size,
      itemSimilarityMatrixSize: this.itemSimilarityMatrix.size
    };
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.userProfiles.clear();
    this.userBehaviors.clear();
    this.itemCatalog.clear();
    this.userItemMatrix.clear();
    this.itemSimilarityMatrix.clear();
    this.userSimilarityMatrix.clear();
    this.recommendationCache.clear();
    this.performanceMonitor.stop();

    Debug.log('优化推荐引擎已销毁');
  }
}
