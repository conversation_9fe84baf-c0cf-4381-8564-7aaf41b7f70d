/**
 * 摄像头动作捕捉系统
 * 集成摄像头输入和MediaPipe进行实时动作捕捉
 */
import { System } from '../core/System';
import type { World } from '../core/World';
import type { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';
import { MotionCaptureSystem } from './MotionCaptureSystem';
import { CameraManager, CameraConfig, CameraState } from './camera/CameraManager';
import { MediaPipePoseDetector, MediaPipeConfig, PoseResults, HandResults } from './mediapipe/MediaPipePoseDetector';
import { ActionMappingSystem } from './interaction/ActionMappingSystem';
import { MotionCaptureComponent } from './components/MotionCaptureComponent';

/**
 * 摄像头动作捕捉配置
 */
export interface CameraMotionCaptureConfig {
  /** 是否启用 */
  enabled: boolean;
  /** 摄像头配置 */
  camera: Partial<CameraConfig>;
  /** MediaPipe配置 */
  mediaPipe: Partial<MediaPipeConfig>;
  /** 是否启用手部追踪 */
  enableHandTracking: boolean;
  /** 是否启用手势识别 */
  enableGestureRecognition: boolean;
  /** 是否启用虚拟交互映射 */
  enableVirtualInteraction: boolean;
  /** 处理间隔（毫秒） */
  processingInterval: number;
  /** 是否启用调试模式 */
  debug: boolean;
  /** 平滑系数 */
  smoothingFactor: number;
  /** 可见度阈值 */
  visibilityThreshold: number;
}

/**
 * 处理统计信息
 */
export interface ProcessingStats {
  /** 总处理帧数 */
  totalFrames: number;
  /** 成功处理帧数 */
  successfulFrames: number;
  /** 平均处理时间（毫秒） */
  averageProcessingTime: number;
  /** 当前FPS */
  currentFPS: number;
  /** 姿态检测成功率 */
  poseDetectionRate: number;
  /** 手部检测成功率 */
  handDetectionRate: number;
}

/**
 * 摄像头动作捕捉系统
 */
export class CameraMotionCaptureSystem extends System {
  /** 系统名称 */
  public static readonly NAME = 'CameraMotionCaptureSystem';

  private config: CameraMotionCaptureConfig;
  private cameraManager: CameraManager;
  private poseDetector: MediaPipePoseDetector;
  private actionMappingSystem: ActionMappingSystem;
  private motionCaptureSystem: MotionCaptureSystem | null = null;
  
  private isProcessing = false;
  private processingTimer: number | null = null;
  private lastProcessTime = 0;
  private stats: ProcessingStats;
  
  private registeredEntities: Map<Entity, MotionCaptureComponent> = new Map();

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: CameraMotionCaptureConfig = {
    enabled: false,
    camera: {
      resolution: { width: 640, height: 480 },
      frameRate: 30,
      audio: false
    },
    mediaPipe: {
      modelComplexity: 1,
      smoothLandmarks: true,
      enableSegmentation: false,
      minDetectionConfidence: 0.5,
      minTrackingConfidence: 0.5,
      enableWorldLandmarks: true
    },
    enableHandTracking: true,
    enableGestureRecognition: true,
    enableVirtualInteraction: true,
    processingInterval: 33, // ~30 FPS
    debug: false,
    smoothingFactor: 0.5,
    visibilityThreshold: 0.1
  };

  constructor(world: World, config: Partial<CameraMotionCaptureConfig> = {}) {
    super(world);
    
    this.config = { ...CameraMotionCaptureSystem.DEFAULT_CONFIG, ...config };
    
    // 初始化统计信息
    this.stats = {
      totalFrames: 0,
      successfulFrames: 0,
      averageProcessingTime: 0,
      currentFPS: 0,
      poseDetectionRate: 0,
      handDetectionRate: 0
    };
    
    // 创建子系统
    this.cameraManager = new CameraManager(this.config.camera);
    this.poseDetector = new MediaPipePoseDetector(this.config.mediaPipe);
    this.actionMappingSystem = new ActionMappingSystem(world);
    
    // 设置事件监听
    this.setupEventListeners();
    
    Debug.log('CameraMotionCaptureSystem', '摄像头动作捕捉系统已创建');
  }

  /**
   * 初始化系统
   */
  public async initialize(): Promise<void> {
    try {
      // 获取现有的动作捕捉系统
      this.motionCaptureSystem = this.world.getSystem('MotionCaptureSystem') as MotionCaptureSystem;
      
      if (!this.config.enabled) {
        Debug.log('CameraMotionCaptureSystem', '系统未启用，跳过初始化');
        return;
      }
      
      // 初始化MediaPipe
      await this.poseDetector.initialize();
      
      // 初始化摄像头
      await this.cameraManager.initialize();
      
      // 开始处理循环
      this.startProcessingLoop();
      
      this.emit('initialized');
      Debug.log('CameraMotionCaptureSystem', '摄像头动作捕捉系统初始化完成');
      
    } catch (error) {
      Debug.error('CameraMotionCaptureSystem', '系统初始化失败', error);
      throw error;
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 摄像头事件
    this.cameraManager.on('stateChanged', (newState: CameraState) => {
      this.emit('cameraStateChanged', newState);
      
      if (newState === CameraState.ACTIVE && this.config.enabled) {
        this.startProcessingLoop();
      } else if (newState !== CameraState.ACTIVE) {
        this.stopProcessingLoop();
      }
    });
    
    this.cameraManager.on('error', (error: Error) => {
      this.emit('cameraError', error);
      Debug.error('CameraMotionCaptureSystem', '摄像头错误', error);
    });
    
    // MediaPipe事件
    this.poseDetector.on('poseDetected', (results: PoseResults, processingTime: number) => {
      this.updateStats('pose', processingTime, true);
      this.emit('poseDetected', results);
    });
    
    this.poseDetector.on('handsDetected', (results: HandResults, processingTime: number) => {
      this.updateStats('hands', processingTime, true);
      this.emit('handsDetected', results);
    });
  }

  /**
   * 开始处理循环
   */
  private startProcessingLoop(): void {
    if (this.isProcessing || !this.config.enabled) {
      return;
    }
    
    this.isProcessing = true;
    
    const processFrame = async () => {
      if (!this.isProcessing || !this.cameraManager.isActive) {
        return;
      }
      
      try {
        const startTime = performance.now();
        
        // 获取当前帧
        const imageData = this.cameraManager.getCurrentFrame();
        if (!imageData) {
          return;
        }
        
        // 处理姿态检测
        const poseResults = await this.poseDetector.detectPose(imageData);
        
        // 处理手部检测（如果启用）
        let handResults: HandResults | null = null;
        if (this.config.enableHandTracking) {
          handResults = await this.poseDetector.detectHands(imageData);
        }
        
        // 应用到注册的实体
        this.applyResultsToEntities(poseResults, handResults);
        
        // 虚拟交互映射（如果启用）
        if (this.config.enableVirtualInteraction) {
          this.processVirtualInteraction(poseResults, handResults);
        }
        
        const processingTime = performance.now() - startTime;
        this.updateStats('total', processingTime, true);
        
        this.emit('frameProcessed', {
          poseResults,
          handResults,
          processingTime
        });
        
      } catch (error) {
        this.updateStats('total', 0, false);
        Debug.error('CameraMotionCaptureSystem', '帧处理失败', error);
        this.emit('processingError', error);
      }
      
      // 调度下一帧
      this.processingTimer = window.setTimeout(processFrame, this.config.processingInterval);
    };
    
    processFrame();
    Debug.log('CameraMotionCaptureSystem', '开始处理循环');
  }

  /**
   * 停止处理循环
   */
  private stopProcessingLoop(): void {
    this.isProcessing = false;
    
    if (this.processingTimer) {
      clearTimeout(this.processingTimer);
      this.processingTimer = null;
    }
    
    Debug.log('CameraMotionCaptureSystem', '停止处理循环');
  }

  /**
   * 应用结果到注册的实体
   */
  private applyResultsToEntities(poseResults: PoseResults, handResults: HandResults | null): void {
    for (const [entity, component] of this.registeredEntities.entries()) {
      try {
        // 更新姿态数据
        if (poseResults.landmarks) {
          component.setLandmarks(poseResults.landmarks);
        }
        
        if (poseResults.worldLandmarks) {
          component.setWorldLandmarks(poseResults.worldLandmarks);
        }
        
        // 如果有动作捕捉系统，使用其解算功能
        if (this.motionCaptureSystem) {
          this.motionCaptureSystem.processEntityMotionCapture(
            entity,
            poseResults.worldLandmarks || [],
            poseResults.landmarks || [],
            this.config.smoothingFactor,
            this.config.visibilityThreshold
          );
        }
        
      } catch (error) {
        Debug.error('CameraMotionCaptureSystem', `应用结果到实体 ${entity.id} 失败`, error);
      }
    }
  }

  /**
   * 处理虚拟交互
   */
  private processVirtualInteraction(poseResults: PoseResults, handResults: HandResults | null): void {
    if (!this.config.enableVirtualInteraction) {
      return;
    }
    
    for (const entity of this.registeredEntities.keys()) {
      try {
        // 处理手部交互
        if (handResults && this.config.enableHandTracking) {
          this.actionMappingSystem.processHandInteraction(entity, handResults);
        }
        
        // 处理姿态交互
        if (poseResults.landmarks) {
          this.actionMappingSystem.processPoseInteraction(entity, poseResults);
        }
        
      } catch (error) {
        Debug.error('CameraMotionCaptureSystem', `处理实体 ${entity.id} 虚拟交互失败`, error);
      }
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(type: 'pose' | 'hands' | 'total', processingTime: number, success: boolean): void {
    this.stats.totalFrames++;
    
    if (success) {
      this.stats.successfulFrames++;
      
      // 更新平均处理时间
      const alpha = 0.1; // 平滑系数
      this.stats.averageProcessingTime = 
        this.stats.averageProcessingTime * (1 - alpha) + processingTime * alpha;
    }
    
    // 计算FPS
    const now = performance.now();
    if (this.lastProcessTime > 0) {
      const deltaTime = now - this.lastProcessTime;
      this.stats.currentFPS = 1000 / deltaTime;
    }
    this.lastProcessTime = now;
    
    // 计算成功率
    this.stats.poseDetectionRate = this.stats.successfulFrames / this.stats.totalFrames;
    this.stats.handDetectionRate = this.stats.successfulFrames / this.stats.totalFrames;
  }

  /**
   * 注册实体
   */
  public registerEntity(entity: Entity, component: MotionCaptureComponent): void {
    this.registeredEntities.set(entity, component);
    this.emit('entityRegistered', entity);
    
    Debug.log('CameraMotionCaptureSystem', `注册实体 ${entity.id}`);
  }

  /**
   * 注销实体
   */
  public unregisterEntity(entity: Entity): void {
    if (this.registeredEntities.delete(entity)) {
      this.emit('entityUnregistered', entity);
      Debug.log('CameraMotionCaptureSystem', `注销实体 ${entity.id}`);
    }
  }

  /**
   * 启用系统
   */
  public async enable(): Promise<void> {
    if (this.config.enabled) {
      return;
    }
    
    this.config.enabled = true;
    await this.initialize();
    this.emit('enabled');
    
    Debug.log('CameraMotionCaptureSystem', '系统已启用');
  }

  /**
   * 禁用系统
   */
  public async disable(): Promise<void> {
    if (!this.config.enabled) {
      return;
    }
    
    this.config.enabled = false;
    this.stopProcessingLoop();
    await this.cameraManager.stop();
    this.emit('disabled');
    
    Debug.log('CameraMotionCaptureSystem', '系统已禁用');
  }

  /**
   * 更新配置
   */
  public async updateConfig(newConfig: Partial<CameraMotionCaptureConfig>): Promise<void> {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };
    
    // 更新子系统配置
    if (newConfig.camera) {
      await this.cameraManager.updateConfig(newConfig.camera);
    }
    
    if (newConfig.mediaPipe) {
      this.poseDetector.updateConfig(newConfig.mediaPipe);
    }
    
    // 如果启用状态发生变化
    if (oldConfig.enabled !== this.config.enabled) {
      if (this.config.enabled) {
        await this.enable();
      } else {
        await this.disable();
      }
    }
    
    this.emit('configUpdated', this.config);
  }

  /**
   * 获取统计信息
   */
  public getStats(): ProcessingStats {
    return { ...this.stats };
  }

  /**
   * 获取配置
   */
  public getConfig(): CameraMotionCaptureConfig {
    return { ...this.config };
  }

  /**
   * 获取摄像头管理器
   */
  public getCameraManager(): CameraManager {
    return this.cameraManager;
  }

  /**
   * 获取姿态检测器
   */
  public getPoseDetector(): MediaPipePoseDetector {
    return this.poseDetector;
  }

  /**
   * 系统更新（继承自System）
   */
  public update(_deltaTime: number): void {
    // 摄像头动作捕捉系统主要通过异步处理循环工作
    // 这里可以添加一些同步的状态检查或清理工作
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    this.disable().catch(error => {
      Debug.error('CameraMotionCaptureSystem', '销毁时禁用系统失败', error);
    });
    
    this.cameraManager.destroy();
    this.poseDetector.destroy();
    this.registeredEntities.clear();
    this.removeAllListeners();
    
    Debug.log('CameraMotionCaptureSystem', '摄像头动作捕捉系统已销毁');
  }
}
