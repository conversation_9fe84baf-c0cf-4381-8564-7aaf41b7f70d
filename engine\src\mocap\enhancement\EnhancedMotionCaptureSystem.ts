/**
 * 增强动作捕捉系统
 * 集成所有增强功能的主系统管理器
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import type { World } from '../../core/World';
import type { Entity } from '../../core/Entity';
import { CameraMotionCaptureSystem } from '../CameraMotionCaptureSystem';
import { PoseEnhancementProcessor } from './PoseEnhancementProcessor';
import { AdvancedGestureRecognizer } from './AdvancedGestureRecognizer';
import { EnhancedHandTracker } from './EnhancedHandTracker';
import { EnhancedInteractionMapper } from './EnhancedInteractionMapper';
import { PerformanceOptimizer } from '../optimization/PerformanceOptimizer';

/**
 * 增强系统配置
 */
export interface EnhancedMotionCaptureConfig {
  /** 是否启用姿态增强 */
  enablePoseEnhancement: boolean;
  /** 是否启用高级手势识别 */
  enableAdvancedGestures: boolean;
  /** 是否启用增强手部追踪 */
  enableEnhancedHandTracking: boolean;
  /** 是否启用增强交互映射 */
  enableEnhancedInteraction: boolean;
  /** 是否启用性能优化 */
  enablePerformanceOptimization: boolean;
  /** 是否启用自动调优 */
  enableAutoTuning: boolean;
  /** 处理质量等级 (0-1) */
  qualityLevel: number;
  /** 性能优先级 (0-1) */
  performancePriority: number;
}

/**
 * 系统状态
 */
export enum SystemState {
  IDLE = 'idle',
  INITIALIZING = 'initializing',
  RUNNING = 'running',
  OPTIMIZING = 'optimizing',
  ERROR = 'error',
  STOPPED = 'stopped'
}

/**
 * 增强动作捕捉系统
 */
export class EnhancedMotionCaptureSystem extends EventEmitter {
  private world: World;
  private config: EnhancedMotionCaptureConfig;
  private state: SystemState = SystemState.IDLE;

  // 核心系统
  private baseSystem: CameraMotionCaptureSystem;
  
  // 增强组件
  private poseEnhancer: PoseEnhancementProcessor | null = null;
  private gestureRecognizer: AdvancedGestureRecognizer | null = null;
  private handTracker: EnhancedHandTracker | null = null;
  private interactionMapper: EnhancedInteractionMapper | null = null;
  private performanceOptimizer: PerformanceOptimizer | null = null;

  // 运行时数据
  private registeredEntities: Set<Entity> = new Set();
  private processingStats = {
    totalFrames: 0,
    successfulFrames: 0,
    averageProcessingTime: 0,
    lastProcessingTime: 0
  };

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: EnhancedMotionCaptureConfig = {
    enablePoseEnhancement: true,
    enableAdvancedGestures: true,
    enableEnhancedHandTracking: true,
    enableEnhancedInteraction: true,
    enablePerformanceOptimization: true,
    enableAutoTuning: true,
    qualityLevel: 0.8,
    performancePriority: 0.6
  };

  constructor(world: World, config: Partial<EnhancedMotionCaptureConfig> = {}) {
    super();
    this.world = world;
    this.config = { ...EnhancedMotionCaptureSystem.DEFAULT_CONFIG, ...config };

    // 创建基础系统
    this.baseSystem = new CameraMotionCaptureSystem(world);
    
    this.setupEventHandlers();
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    // 基础系统事件
    this.baseSystem.on('poseDetected', (data) => {
      this.handlePoseDetected(data);
    });

    this.baseSystem.on('handsDetected', (data) => {
      this.handleHandsDetected(data);
    });

    this.baseSystem.on('error', (error) => {
      this.handleSystemError(error);
    });
  }

  /**
   * 初始化增强系统
   */
  public async initialize(): Promise<void> {
    try {
      this.setState(SystemState.INITIALIZING);
      
      Debug.log('EnhancedMotionCaptureSystem', '开始初始化增强动作捕捉系统');

      // 初始化基础系统
      await this.baseSystem.initialize();

      // 初始化增强组件
      await this.initializeEnhancementComponents();

      // 初始化性能优化器
      if (this.config.enablePerformanceOptimization) {
        await this.initializePerformanceOptimizer();
      }

      this.setState(SystemState.RUNNING);
      
      Debug.log('EnhancedMotionCaptureSystem', '增强动作捕捉系统初始化完成');
      this.emit('initialized');

    } catch (error) {
      this.setState(SystemState.ERROR);
      Debug.error('EnhancedMotionCaptureSystem', '系统初始化失败', error);
      throw error;
    }
  }

  /**
   * 初始化增强组件
   */
  private async initializeEnhancementComponents(): Promise<void> {
    // 姿态增强处理器
    if (this.config.enablePoseEnhancement) {
      this.poseEnhancer = new PoseEnhancementProcessor({
        enableModelFusion: true,
        enableTemporalSmoothing: true,
        smoothingStrength: this.config.qualityLevel
      });
      Debug.log('EnhancedMotionCaptureSystem', '姿态增强处理器已初始化');
    }

    // 高级手势识别器
    if (this.config.enableAdvancedGestures) {
      this.gestureRecognizer = new AdvancedGestureRecognizer({
        enableDynamicGestures: true,
        enableSequenceRecognition: true,
        confidenceThreshold: 0.6 + this.config.qualityLevel * 0.2
      });
      Debug.log('EnhancedMotionCaptureSystem', '高级手势识别器已初始化');
    }

    // 增强手部追踪器
    if (this.config.enableEnhancedHandTracking) {
      this.handTracker = new EnhancedHandTracker({
        enableFineFingerTracking: true,
        enableMotionAnalysis: true,
        smoothingFactor: this.config.qualityLevel
      });
      Debug.log('EnhancedMotionCaptureSystem', '增强手部追踪器已初始化');
    }

    // 增强交互映射器
    if (this.config.enableEnhancedInteraction) {
      this.interactionMapper = new EnhancedInteractionMapper(this.world, {
        enablePredictiveInteraction: true,
        enableCollaborativeInteraction: true,
        precisionThreshold: this.config.qualityLevel
      });
      Debug.log('EnhancedMotionCaptureSystem', '增强交互映射器已初始化');
    }
  }

  /**
   * 初始化性能优化器
   */
  private async initializePerformanceOptimizer(): Promise<void> {
    this.performanceOptimizer = new PerformanceOptimizer({
      strategy: this.config.performancePriority > 0.7 ? 'performance_first' : 'balanced',
      enableAdaptiveOptimization: this.config.enableAutoTuning,
      targetFPS: 30
    });

    // 设置优化事件监听
    this.performanceOptimizer.on('qualityAdjustment', (data) => {
      this.handleQualityAdjustment(data.factor);
    });

    this.performanceOptimizer.on('frequencyAdjustment', (data) => {
      this.handleFrequencyAdjustment(data.factor);
    });

    Debug.log('EnhancedMotionCaptureSystem', '性能优化器已初始化');
  }

  /**
   * 处理姿态检测
   */
  private async handlePoseDetected(data: any): Promise<void> {
    const startTime = performance.now();
    
    try {
      this.processingStats.totalFrames++;

      let enhancedData = data;

      // 姿态增强处理
      if (this.poseEnhancer && data.poseResults) {
        enhancedData.poseResults = this.poseEnhancer.processPose(data.poseResults);
      }

      // 处理注册的实体
      for (const entity of this.registeredEntities) {
        await this.processEntityPose(entity, enhancedData);
      }

      this.processingStats.successfulFrames++;
      
      // 记录性能数据
      if (this.performanceOptimizer) {
        this.performanceOptimizer.recordFrameTime();
      }

      this.emit('enhancedPoseDetected', enhancedData);

    } catch (error) {
      Debug.error('EnhancedMotionCaptureSystem', '姿态处理失败', error);
    } finally {
      const processingTime = performance.now() - startTime;
      this.processingStats.lastProcessingTime = processingTime;
      this.updateAverageProcessingTime(processingTime);
    }
  }

  /**
   * 处理手部检测
   */
  private async handleHandsDetected(data: any): Promise<void> {
    try {
      let enhancedData = data;

      // 增强手部追踪
      if (this.handTracker && data.handResults) {
        const enhancedHandData = this.handTracker.processHandTracking(data.handResults);
        enhancedData.enhancedHandData = enhancedHandData;

        // 高级手势识别
        if (this.gestureRecognizer) {
          if (enhancedHandData.leftHand) {
            enhancedData.leftAdvancedGesture = this.gestureRecognizer.recognizeGesture(
              enhancedHandData.leftHand.landmarks, 'left'
            );
          }
          if (enhancedHandData.rightHand) {
            enhancedData.rightAdvancedGesture = this.gestureRecognizer.recognizeGesture(
              enhancedHandData.rightHand.landmarks, 'right'
            );
          }
        }
      }

      // 增强交互处理
      if (this.interactionMapper) {
        for (const entity of this.registeredEntities) {
          await this.processEntityInteraction(entity, enhancedData);
        }
      }

      this.emit('enhancedHandsDetected', enhancedData);

    } catch (error) {
      Debug.error('EnhancedMotionCaptureSystem', '手部处理失败', error);
    }
  }

  /**
   * 处理实体姿态
   */
  private async processEntityPose(entity: Entity, data: any): Promise<void> {
    const motionCaptureComponent = entity.getComponent('MotionCaptureComponent');
    if (!motionCaptureComponent) return;

    // 更新实体姿态数据
    if (data.poseResults) {
      motionCaptureComponent.updatePoseData(data.poseResults);
    }
  }

  /**
   * 处理实体交互
   */
  private async processEntityInteraction(entity: Entity, data: any): Promise<void> {
    if (!this.interactionMapper) return;

    const targetObjects = this.world.getEntities().filter(e => 
      e !== entity && e.hasComponent('GrabbableComponent')
    );

    this.interactionMapper.processEnhancedInteraction(
      entity,
      data.leftAdvancedGesture,
      data.rightAdvancedGesture,
      data.poseResults,
      targetObjects
    );
  }

  /**
   * 处理质量调整
   */
  private handleQualityAdjustment(factor: number): void {
    this.config.qualityLevel = Math.max(0.1, Math.min(1.0, this.config.qualityLevel * factor));
    
    // 更新各组件的质量设置
    if (this.poseEnhancer) {
      this.poseEnhancer.updateConfig({
        smoothingStrength: this.config.qualityLevel
      });
    }

    if (this.gestureRecognizer) {
      this.gestureRecognizer.updateConfig({
        confidenceThreshold: 0.6 + this.config.qualityLevel * 0.2
      });
    }

    if (this.handTracker) {
      this.handTracker.updateConfig({
        smoothingFactor: this.config.qualityLevel
      });
    }

    Debug.log('EnhancedMotionCaptureSystem', `质量调整: ${factor}, 新质量等级: ${this.config.qualityLevel}`);
  }

  /**
   * 处理频率调整
   */
  private handleFrequencyAdjustment(factor: number): void {
    // 调整处理频率
    const newFrequency = Math.max(15, Math.min(60, 30 * factor));
    
    // 更新基础系统的处理频率
    this.baseSystem.updateConfig({
      camera: {
        frameRate: newFrequency
      }
    });

    Debug.log('EnhancedMotionCaptureSystem', `频率调整: ${factor}, 新频率: ${newFrequency}fps`);
  }

  /**
   * 处理系统错误
   */
  private handleSystemError(error: Error): void {
    Debug.error('EnhancedMotionCaptureSystem', '系统错误', error);
    
    if (this.performanceOptimizer) {
      this.performanceOptimizer.addTask({
        id: `error_recovery_${Date.now()}`,
        type: 'error_recovery',
        data: { error },
        priority: 10,
        timeout: 5000,
        maxRetries: 3
      });
    }

    this.emit('systemError', error);
  }

  /**
   * 更新平均处理时间
   */
  private updateAverageProcessingTime(processingTime: number): void {
    const alpha = 0.1; // 平滑系数
    this.processingStats.averageProcessingTime = 
      this.processingStats.averageProcessingTime * (1 - alpha) + processingTime * alpha;
  }

  /**
   * 设置系统状态
   */
  private setState(newState: SystemState): void {
    if (this.state !== newState) {
      const oldState = this.state;
      this.state = newState;
      this.emit('stateChanged', { oldState, newState });
    }
  }

  /**
   * 注册实体
   */
  public registerEntity(entity: Entity): void {
    this.registeredEntities.add(entity);
    this.baseSystem.registerEntity(entity);
    this.emit('entityRegistered', entity);
  }

  /**
   * 注销实体
   */
  public unregisterEntity(entity: Entity): void {
    this.registeredEntities.delete(entity);
    this.baseSystem.unregisterEntity(entity);
    this.emit('entityUnregistered', entity);
  }

  /**
   * 启用系统
   */
  public async enable(): Promise<void> {
    await this.baseSystem.enable();
    this.setState(SystemState.RUNNING);
    this.emit('enabled');
  }

  /**
   * 禁用系统
   */
  public async disable(): Promise<void> {
    await this.baseSystem.disable();
    this.setState(SystemState.STOPPED);
    this.emit('disabled');
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<EnhancedMotionCaptureConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 更新各组件配置
    if (this.poseEnhancer && newConfig.enablePoseEnhancement !== undefined) {
      // 根据需要重新初始化组件
    }

    this.emit('configUpdated', this.config);
  }

  /**
   * 获取配置
   */
  public getConfig(): EnhancedMotionCaptureConfig {
    return { ...this.config };
  }

  /**
   * 获取系统状态
   */
  public getState(): SystemState {
    return this.state;
  }

  /**
   * 获取处理统计
   */
  public getProcessingStats(): any {
    return {
      ...this.processingStats,
      successRate: this.processingStats.totalFrames > 0 ? 
        this.processingStats.successfulFrames / this.processingStats.totalFrames : 0,
      performanceMetrics: this.performanceOptimizer?.getMetrics() || null
    };
  }

  /**
   * 销毁系统
   */
  public async destroy(): Promise<void> {
    this.setState(SystemState.STOPPED);
    
    // 销毁所有组件
    if (this.performanceOptimizer) {
      this.performanceOptimizer.destroy();
    }
    
    if (this.poseEnhancer) {
      this.poseEnhancer.reset();
    }
    
    if (this.gestureRecognizer) {
      this.gestureRecognizer.reset();
    }
    
    if (this.handTracker) {
      this.handTracker.reset();
    }
    
    if (this.interactionMapper) {
      this.interactionMapper.reset();
    }

    await this.baseSystem.destroy();
    
    this.registeredEntities.clear();
    this.removeAllListeners();
    
    this.emit('destroyed');
  }
}
